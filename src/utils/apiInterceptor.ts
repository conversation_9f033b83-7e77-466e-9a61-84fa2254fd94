interface ApiRequestOptions extends RequestInit {
  headers?: HeadersInit;
}

interface ApiResponse<T = any> {
  code?: number;
  message: string;
  data?: T;
  success?: boolean;
}

// 创建一个增强的fetch函数，自动处理401错误
export const apiRequest = async <T = any>(
  url: string,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> => {
  try {
    // 默认headers
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // 自动添加Authorization头
    const token = localStorage.getItem('dy-token');
    if (token) {
      (defaultHeaders as any)['Authorization'] = `Bearer ${token}`;
    }

    // 合并headers
    const mergedHeaders = {
      ...defaultHeaders,
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers: mergedHeaders,
    });

    // 检查401错误
    if (response.status === 401) {
      console.warn('检测到401错误，正在跳转到登录页...');
      
      // 清除本地存储的认证信息
      localStorage.removeItem('dy-token');
      localStorage.removeItem('token_type');
      localStorage.removeItem('user_type');
      localStorage.removeItem('testWarningAgreed');
      
      // 显示提示消息
      if (typeof window !== 'undefined') {
        // 动态导入message以避免SSR问题
        import('antd').then(({ message }) => {
          message.error('登录已过期，请重新登录');
        });
      }
      
      // 延迟跳转，确保提示消息能够显示
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          window.location.href = '/';
        }
      }, 1000);
      
      return {
        code: 401,
        message: 'Could not validate credentials',
        success: false
      };
    }

    const data = await response.json();

    // 处理其他错误状态码
    if (!response.ok) {
      return {
        code: response.status,
        message: data.message || '请求失败',
        success: false,
        data: data.data
      };
    }

    // 处理成功响应
    return {
      ...data,
      success: data.code === 200 || response.ok
    };

  } catch (error) {
    console.error('API请求失败:', error);
    return {
      message: '网络错误，请稍后再试',
      success: false
    };
  }
};

// 创建一个专门用于需要认证的API请求的函数
export const authenticatedApiRequest = async <T = any>(
  url: string,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> => {
  const token = localStorage.getItem('dy-token');
  
  if (!token) {
    console.warn('未找到登录凭证，跳转到登录页');
    
    if (typeof window !== 'undefined') {
      import('antd').then(({ message }) => {
        message.error('请先登录');
      });
      
      setTimeout(() => {
        window.location.href = '/';
      }, 1000);
    }
    
    return {
      code: 401,
      message: '请先登录',
      success: false
    };
  }

  return apiRequest<T>(url, options);
};

// 导出一个便捷的API调用函数
export const api = {
  get: <T = any>(url: string, options?: Omit<ApiRequestOptions, 'method'>) =>
    authenticatedApiRequest<T>(url, { ...options, method: 'GET' }),
    
  post: <T = any>(url: string, data?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>) =>
    authenticatedApiRequest<T>(url, { 
      ...options, 
      method: 'POST', 
      body: data ? JSON.stringify(data) : undefined 
    }),
    
  put: <T = any>(url: string, data?: any, options?: Omit<ApiRequestOptions, 'method' | 'body'>) =>
    authenticatedApiRequest<T>(url, { 
      ...options, 
      method: 'PUT', 
      body: data ? JSON.stringify(data) : undefined 
    }),
    
  delete: <T = any>(url: string, options?: Omit<ApiRequestOptions, 'method'>) =>
    authenticatedApiRequest<T>(url, { ...options, method: 'DELETE' }),
}; 