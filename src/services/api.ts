import { API_CONFIG, API_ENDPOINTS } from '../config/api';
import { apiRequest, authenticatedApiRequest } from '../utils/apiInterceptor';

interface LoginParams {
  username: string;
  password: string;
}

interface LoginSuccessData {
  access_token: string;
  token_type: string;
  user_type?: number;
}

interface ApiResponse<T = any> {
  code?: number;
  message: string;
  data?: T;
  success?: boolean;
}

// 用户列表接口参数
interface UserListParams {
  page: number;
  page_size: number;
  user_type?: number;
}

// 用户信息接口
interface UserInfo {
  id: number;
  username: string;
  user_type: number;
  transcription_count: number;
  summary_count: number;
  created_at: string;
  modified_at: string;
}

// 用户列表响应数据
interface UserListData {
  items: UserInfo[];
  total: number;
  page?: number;
  page_size?: number;
}

// 新增用户接口参数
interface UserRegisterParams {
  username: string;
  password: string;
  user_type: number;
}

// 登录API（不需要认证）
export const loginApi = async (params: LoginParams): Promise<ApiResponse<LoginSuccessData>> => {
  return apiRequest<LoginSuccessData>(`${API_CONFIG.baseURL}${API_ENDPOINTS.login}`, {
    method: 'POST',
    body: JSON.stringify(params),
  });
};

// 获取用户列表（需要认证）
export const getUserListApi = async (params: UserListParams): Promise<ApiResponse<UserListData>> => {
  const queryParams = new URLSearchParams({
    page: params.page.toString(),
    page_size: params.page_size.toString(),
    ...(params.user_type && { user_type: params.user_type.toString() })
  });

  return authenticatedApiRequest<UserListData>(`${API_CONFIG.baseURL}${API_ENDPOINTS.userList}?${queryParams}`, {
    method: 'GET',
  });
};

// 新增用户（需要认证）
export const registerUserApi = async (params: UserRegisterParams): Promise<ApiResponse> => {
  return authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.userRegister}`, {
    method: 'POST',
    body: JSON.stringify(params),
  });
}; 