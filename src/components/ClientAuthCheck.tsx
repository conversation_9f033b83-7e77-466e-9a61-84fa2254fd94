'use client'

import { ReactNode, Suspense } from 'react'
import dynamic from 'next/dynamic'

// 动态导入 AuthCheck 组件，避免服务端渲染时的 localStorage 错误
const AuthCheck = dynamic(() => import('./AuthCheck'), {
  ssr: false,
})

interface ClientAuthCheckProps {
  children: ReactNode
}

const ClientAuthCheck = ({ children }: ClientAuthCheckProps) => {
  return (
    <Suspense fallback={null}>
      <AuthCheck>{children}</AuthCheck>
    </Suspense>
  )
}

export default ClientAuthCheck 