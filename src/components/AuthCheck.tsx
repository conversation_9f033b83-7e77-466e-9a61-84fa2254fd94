'use client'
import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'

interface AuthCheckProps {
  children: React.ReactNode
}

const AuthCheck: React.FC<AuthCheckProps> = ({ children }) => {
  const router = useRouter()
  const pathname = usePathname()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 检查本地存储中是否有token
    const token = localStorage.getItem('dy-token')
    
    // 登录页面的特殊处理
    if (pathname === '/login') {
      // 如果已经登录并且在登录页面，自动重定向到仪表盘
      if (token) {
        router.push('/dashboard')
      } else {
        // 未登录状态下在登录页是正常的，允许访问
        setIsAuthenticated(true)
      }
      setLoading(false)
      return
    }
    
    // 其他页面需要验证token
    if (!token) {
      // 没有token，跳转到登录页面
      router.push('/')
    } else {
      // 有token，设置认证状态为true
      setIsAuthenticated(true)
    }
    
    setLoading(false)
  }, [pathname, router])

  // 如果在加载中，显示简单的加载指示器
  if (loading) {
    return <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh' 
    }}>
      加载中...
    </div>
  }

  // 如果已认证或在登录页面，渲染子组件
  if (isAuthenticated) {
    return <>{children}</>
  }

  // 默认情况下不渲染任何内容（应该不会到达这里，因为未认证会重定向）
  return null
}

export default AuthCheck 