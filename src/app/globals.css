@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-color: #FFCE3A;
  --secondary-color: #333333;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary-color);
  --color-secondary: var(--secondary-color);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary-color: #FFCE3A;
    --secondary-color: #E0E0E0;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@tailwind base;

@layer base {
    h1 {
        @apply text-2xl font-bold mb-2 mt-6;
    }
    h2 {
        @apply text-xl font-bold mb-2 mt-5;
    }
    h3 {
        @apply text-lg font-semibold mb-2 mt-4;
    }
    h4 {
        @apply text-base font-semibold mb-2 mt-3;
    }
    h5 {
        @apply text-sm font-semibold mb-1 mt-2;
    }
    h6 {
        @apply text-xs font-semibold mb-1 mt-2;
    }
    ul {
        @apply list-disc list-outside pl-5 my-4;
    }
    ol {
        @apply list-decimal list-outside pl-5 my-4;
    }
    li {
        @apply my-1 pl-1;
    }
    li > ul,
    li > ol {
        @apply my-0 ml-4;
    }
}

@tailwind utilities;
