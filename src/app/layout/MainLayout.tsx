'use client'
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, message, Modal } from 'antd';
import { 
  TeamOutlined, 
  SettingOutlined, 
  LogoutOutlined,
  FileTextOutlined,
  UserAddOutlined,
  ReadOutlined,
  CustomerServiceOutlined,
  ExperimentOutlined,
  ProjectOutlined,
  ApiOutlined,
  LineChartOutlined,
  UserOutlined,
  AppstoreOutlined,
  RocketOutlined,
  ApiTwoTone,
  FolderOpenOutlined,
  ScheduleOutlined,
  ApartmentOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  MenuFoldOutlined,
  DesktopOutlined,
  RobotOutlined,
  UsergroupAddOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useRouter } from 'next/navigation';

// 导入各个页面组件
import AudioTranscriptionPage from '../pages/audio-transcription/page';
import ModelMarketPage from '../pages/model-market/page';
import ChatExperiencePage from '../pages/chat-experience/page';
import DefaultPage from '../pages/default/page';
import UserManagementPage from '../pages/user-management/page';

const { Sider, Content, Header } = Layout;

// 定义菜单项类型
type MenuItem = {
  key: string;
  label: string;
  icon: React.ReactNode;
  children?: {
    key: string;
    label: string;
    icon: React.ReactNode;
  }[];
}

const MainLayout = () => {
  const router = useRouter();
  const [currentMenu, setCurrentMenu] = useState<string>('model-market');
  const [showDashboard, setShowDashboard] = useState<boolean>(false);
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const [userType, setUserType] = useState<number | null>(null);
  
  // 在组件挂载时获取用户类型
  useEffect(() => {
    const storedUserType = localStorage.getItem('user_type');
    if (storedUserType) {
      setUserType(parseInt(storedUserType, 10));
      
      // 如果是用户类型3，确保默认显示体验模型页面
      if (parseInt(storedUserType, 10) === 3) {
        setCurrentMenu('model-experience');
        // 直接触发显示电牙模型的dashboard事件
        setTimeout(() => {
          const dashboardEvent = new Event('showDashboard');
          document.body.dispatchEvent(dashboardEvent);
        }, 100);
      }
    }
  }, []);
  
  // 菜单项配置
  const menuItems: MenuItem[] = [
    {
      key: 'collaboration',
      label: '邀请协作',
      icon: <UserAddOutlined />
    },
    {
      key: 'docs',
      label: '文档中心',
      icon: <ReadOutlined />
    },
    {
      key: 'customer-service',
      label: '添加客服',
      icon: <CustomerServiceOutlined />
    },
    {
      key: 'model',
      label: '模型选型',
      icon: <ExperimentOutlined />,
      children: [
        {
          key: 'model-market',
          label: '模型超市',
          icon: <AppstoreOutlined />
        },
        {
          key: 'model-experience',
          label: '体验模型',
          icon: <RocketOutlined />
        }
      ]
    },
    {
      key: 'project',
      label: '项目管理',
      icon: <ProjectOutlined />,
      children: [
        {
          key: 'new-project',
          label: '新发起',
          icon: <FolderOpenOutlined />
        },
        {
          key: 'todo',
          label: '待办事项',
          icon: <ScheduleOutlined />
        },
        {
          key: 'project-list',
          label: '项目列表',
          icon: <ApartmentOutlined />
        },
        {
          key: 'in-progress',
          label: '执行中/运维中',
          icon: <ClockCircleOutlined />
        },
        {
          key: 'completed',
          label: '已结项',
          icon: <CheckCircleOutlined />
        }
      ]
    },
    {
      key: 'api',
      label: 'API中心',
      icon: <ApiOutlined />
    },
    {
      key: 'monitoring',
      label: '数据监测',
      icon: <LineChartOutlined />
    },
    {
      key: 'account',
      label: '账户信息',
      icon: <UserOutlined />
    },
    {
      key: 'user-management',
      label: '用户管理',
      icon: <UsergroupAddOutlined />
    }
  ];

  // 根据用户类型过滤菜单
  const filteredMenuItems = () => {
    // 用户类型为1（管理员）时，显示全部菜单包括用户管理
    if (userType === 1) {
      return menuItems;
    }
    // 用户类型为2时，显示全部菜单但不包括用户管理
    else if (userType === 2) {
      return menuItems.filter(item => item.key !== 'user-management');
    } 
    // 用户类型为3时，不显示任何菜单，直接进入体验模型
    else if (userType === 3) {
      return [];
    }
    // 默认显示全部菜单但不包括用户管理
    return menuItems.filter(item => item.key !== 'user-management');
  };
  
  // 监听自定义事件，从dashboard页面返回
  useEffect(() => {
    const handleBackToMain = () => {
      // 如果是用户类型3，不允许返回，保持在dashboard状态
      if (userType === 3) {
        return;
      }
      
      setShowDashboard(false);
      // 固定返回到模型超市界面
      setCurrentMenu('model-market');
    };
    
    // 监听显示dashboard事件
    const handleShowDashboard = () => {
      setShowDashboard(true);
    };
    
    // 监听菜单点击事件
    const handleMenuClickEvent = (event: CustomEvent) => {
      const { key } = event.detail;
      if (key) {
        setCurrentMenu(key);
        
        // 如果正在显示dashboard且切换到非体验模型菜单，则返回主界面
        if (showDashboard && key !== 'model-experience') {
          setShowDashboard(false);
        }
      }
    };
    
    // 添加事件监听
    document.body.addEventListener('backToMain', handleBackToMain);
    document.body.addEventListener('showDashboard', handleShowDashboard);
    document.body.addEventListener('menuClick', handleMenuClickEvent as EventListener);
    
    return () => {
      // 移除事件监听
      document.body.removeEventListener('backToMain', handleBackToMain);
      document.body.removeEventListener('showDashboard', handleShowDashboard);
      document.body.removeEventListener('menuClick', handleMenuClickEvent as EventListener);
    };
  }, [showDashboard]);
  
  // 处理菜单点击事件
  const handleMenuClick: MenuProps['onClick'] = (e) => {
    const key = e.key;
    
    // 处理模型超市点击逻辑
    if (key === 'model-market') {
      // 切换到模型超市页面
      setCurrentMenu('model-market');
      
      // 如果正在显示dashboard，则返回主界面
      if (showDashboard) {
        setShowDashboard(false);
      }
      return;
    }
    
    // 处理体验模型点击 - 直接跳转到电牙模型
    if (key === 'model-experience') {
      setCurrentMenu('model-experience');
      
      // 直接触发显示电牙模型的dashboard事件
      const dashboardEvent = new Event('showDashboard');
      document.body.dispatchEvent(dashboardEvent);
      return;
    }
    
    // 对于其他所有菜单项，显示"开发中"页面
    setCurrentMenu(key);
    
    // 如果正在显示dashboard，则返回主界面
    if (showDashboard) {
      setShowDashboard(false);
    }
  };
  
  // 退出登录
  const [logoutModalVisible, setLogoutModalVisible] = useState(false);

  const handleLogout = () => {
    setLogoutModalVisible(true);
  };

  const confirmLogout = () => {
    localStorage.removeItem('dy-token');
    localStorage.removeItem('token_type');
    localStorage.removeItem('user_type');
    localStorage.removeItem('testWarningAgreed');
    message.success('已退出登录');
    // 刷新页面让主页判断登录状态
    window.location.reload();
  };
  
  // 渲染当前菜单对应的内容
  const renderContent = () => {
    // 如果显示dashboard，则优先显示
    if (showDashboard) {
      return <AudioTranscriptionPage />;
    }
    
    // 根据当前菜单显示不同内容
    switch (currentMenu) {
      case 'model-market':
        return <ModelMarketPage />;
      case 'model-experience':
        // 从sessionStorage获取模型信息
        const storedModel = sessionStorage.getItem('selectedModel');
        if (storedModel) {
          return <ChatExperiencePage />;
        }
        // 否则显示默认页面
        return <DefaultPage />;
      case 'user-management':
        return <UserManagementPage />;
      default:
        // 所有其他菜单都显示"开发中"页面
        return <DefaultPage />;
    }
  };
  
  // 计算当前选中的菜单项
  const getSelectedKeys = () => {
    if (showDashboard) {
      return ['model-experience'];
    }
    return [currentMenu];
  }
  
  return (
    <Layout style={{ height: '100vh', overflow: 'hidden' }}>
      <Layout>
        {/* 左侧边栏 - 用户类型3时隐藏 */}
        {userType !== 3 && (
          <Sider 
            width={240} 
            style={{ height: '100vh', background: '#00152A', borderRight: '1px solid #f0f0f0', overflowY: 'auto' }}
            collapsed={collapsed}
            collapsible
            trigger={null}
          >
            {/* Logo区域 */}
            <div style={{ 
              padding: '12px', 
              textAlign: 'center', 
              borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
              backgroundColor: '#01274e',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '50px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <img src="/logo1.svg" alt="Logo1" style={{ height: '40px', marginRight: collapsed ? '0' : '2px' }} />
                {!collapsed && <img src="/logo2.svg" alt="Logo2" style={{ height: '40px', marginLeft: '-20px' }} />}
              </div>
            </div>
            
            {/* 导航菜单 */}
            <Menu
              mode="inline"
              selectedKeys={getSelectedKeys()}
              defaultOpenKeys={['model', 'project']}
              onClick={handleMenuClick}
              style={{ 
                height: 'calc(100% - 50px)', 
                borderRight: 0,
                backgroundColor: '#00152A',
                color: '#ffffff'
              }}
              items={filteredMenuItems().map(item => ({
                key: item.key,
                icon: item.icon,
                label: item.label,
                children: item.children?.map(child => ({
                  key: child.key,
                  label: child.label,
                  icon: child.icon
                }))
              }))}
              theme="dark"
              // 自定义菜单样式
              className="custom-menu"
            />
          </Sider>
        )}
        
        {/* 右侧内容区域 */}
        <Layout>
          {/* 顶部辅助菜单栏 - 用户类型3时简化 */}
          <Header style={{ 
            background: '#fff', 
            padding: '0 16px', 
            height: '50px', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            borderBottom: '1px solid #f0f0f0'
          }}>
            {/* 用户类型3时不显示折叠按钮 */}
            {userType !== 3 && (
              <Button 
                icon={<MenuFoldOutlined />} 
                onClick={() => setCollapsed(!collapsed)}
                type="text"
              />
            )}
            {/* 用户类型3时显示占位符 */}
            {userType === 3 && <div></div>}
            
            <Button 
              icon={<LogoutOutlined />} 
              onClick={handleLogout}
              type="text"
            >
              退出登录
            </Button>
          </Header>
          
          <Content style={{ height: 'calc(100vh - 50px)', overflow: 'auto', position: 'relative' }}>
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
      
      {/* 添加自定义样式 */}
      <style jsx global>{`
        .custom-menu .ant-menu-item-selected {
          background-color: rgba(255, 206, 57, 1) !important;
          color: #000000 !important;
          border-radius: 0 !important;
        }
        .custom-menu .ant-menu-submenu-selected > .ant-menu-submenu-title {
          color: rgba(255, 206, 57, 1) !important;
        }
        .custom-menu .ant-menu-item:hover,
        .custom-menu .ant-menu-submenu-title:hover {
          color: #ffffff !important;
        }
        .custom-menu .ant-menu-sub.ant-menu-inline {
          background: #00152A !important;
        }
        .custom-menu .ant-menu-item {
          margin: 0 !important;
          width: 100% !important;
        }
        .custom-menu .ant-menu-submenu-title {
          margin: 0 !important;
          width: 100% !important;
        }
        .custom-menu .ant-menu-submenu-inline > .ant-menu-submenu-title {
          padding-left: 24px !important;
        }
        .custom-menu .ant-menu-inline .ant-menu-item {
          padding-left: 48px !important;
        }
        .custom-menu .ant-menu-submenu {
          padding-left: 0 !important;
        }
      `}</style>

      {/* 退出确认弹窗 */}
      <Modal
        title="确认退出登录"
        open={logoutModalVisible}
        onOk={confirmLogout}
        onCancel={() => setLogoutModalVisible(false)}
        okText="确认退出"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>确定要退出登录吗？</p>
      </Modal>
    </Layout>
  );
};

// 为window添加enterDashboard方法的类型声明
declare global {
  interface Window {
    enterDashboard?: () => void;
  }
}

export default MainLayout; 