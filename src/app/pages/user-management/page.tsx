'use client'
import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Card, 
  Typography, 
  Space, 
  Tag,
  Pagination,
  Row,
  Col
} from 'antd';
import { 
  UserAddOutlined, 
  UserOutlined, 
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { getUserListApi, registerUserApi } from '../../../services/api';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

const { Title } = Typography;
const { Option } = Select;

interface UserInfo {
  id: number;
  username: string;
  user_type: number;
  transcription_count: number;
  summary_count: number;
  created_at: string;
  modified_at: string;
}

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);
  const [userTypeFilter, setUserTypeFilter] = useState<number | undefined>(undefined);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  // 用户类型映射
  const userTypeMap = {
    1: { label: '管理员', color: 'red' },
    2: { label: '普通用户', color: 'blue' },
    3: { label: '体验用户', color: 'green' }
  };

  // 获取用户列表
  const fetchUsers = async (page: number = currentPage, userType?: number) => {
    setLoading(true);
    try {
      const response = await getUserListApi({
        page,
        page_size: pageSize,
        ...(userType && { user_type: userType })
      });

      if (response.success && response.data) {
        setUsers(response.data.items || []);
        setTotal(response.data.total || 0);
      } else {
        message.error(response.message || '获取用户列表失败');
      }
    } catch (error) {
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取所有用户数据用于导出
  const fetchAllUsers = async (userType?: number): Promise<UserInfo[]> => {
    try {
      const response = await getUserListApi({
        page: 1,
        page_size: 1000, // 获取大量数据
        ...(userType && { user_type: userType })
      });

      if (response.success && response.data) {
        return response.data.items || [];
      } else {
        message.error(response.message || '获取用户数据失败');
        return [];
      }
    } catch (error) {
      message.error('获取用户数据失败');
      return [];
    }
  };

  // 导出Excel功能
  const handleExportExcel = async () => {
    setExportLoading(true);
    try {
      // 获取所有用户数据（根据当前筛选条件）
      const allUsers = await fetchAllUsers(userTypeFilter);
      
      if (allUsers.length === 0) {
        message.warning('没有数据可导出');
        return;
      }

      // 准备导出数据
      const exportData = allUsers.map((user, index) => ({
        '序号': index + 1,
        'ID': user.id,
        '用户名': user.username,
        '用户类型': userTypeMap[user.user_type as keyof typeof userTypeMap]?.label || '未知类型',
        '转写数量': user.transcription_count,
        '总结数量': user.summary_count,
        '创建时间': new Date(user.created_at).toLocaleString('zh-CN')
      }));

      // 创建工作簿
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '用户列表');

      // 设置列宽
      const colWidths = [
        { wch: 8 },  // 序号
        { wch: 8 },  // ID
        { wch: 15 }, // 用户名
        { wch: 12 }, // 用户类型
        { wch: 12 }, // 转写数量
        { wch: 12 }, // 总结数量
        { wch: 20 }  // 创建时间
      ];
      worksheet['!cols'] = colWidths;

      // 生成Excel文件
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // 生成文件名
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
      const filterStr = userTypeFilter ? `_${userTypeMap[userTypeFilter as keyof typeof userTypeMap]?.label}` : '';
      const fileName = `用户列表${filterStr}_${dateStr}_${timeStr}.xlsx`;
      
      // 下载文件
      saveAs(data, fileName);
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请稍后重试');
    } finally {
      setExportLoading(false);
    }
  };

  // 新增用户
  const handleAddUser = async (values: any) => {
    try {
      const response = await registerUserApi({
        username: values.username,
        password: values.password,
        user_type: values.user_type
      });

      if (response.success) {
        message.success('用户添加成功');
        setModalVisible(false);
        form.resetFields();
        fetchUsers(1, userTypeFilter); // 重新获取第一页数据，保持筛选条件
        setCurrentPage(1);
      } else {
        message.error(response.message || '添加用户失败');
      }
    } catch (error) {
      message.error('添加用户失败');
    }
  };

  // 处理用户类型筛选变化
  const handleUserTypeFilterChange = (value: number | undefined) => {
    setUserTypeFilter(value);
    setCurrentPage(1);
    fetchUsers(1, value);
  };

  // 页面加载时获取用户列表
  useEffect(() => {
    fetchUsers();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '用户类型',
      dataIndex: 'user_type',
      key: 'user_type',
      render: (type: number) => {
        const typeInfo = userTypeMap[type as keyof typeof userTypeMap];
        return typeInfo ? (
          <Tag color={typeInfo.color}>{typeInfo.label}</Tag>
        ) : (
          <Tag>未知类型</Tag>
        );
      },
    },
    {
      title: '转写数量',
      dataIndex: 'transcription_count',
      key: 'transcription_count',
      width: 100,
      render: (count: number) => (
        <span style={{ color: count > 0 ? '#1890ff' : '#999' }}>
          {count}
        </span>
      ),
    },
    {
      title: '总结数量',
      dataIndex: 'summary_count',
      key: 'summary_count',
      width: 100,
      render: (count: number) => (
        <span style={{ color: count > 0 ? '#52c41a' : '#999' }}>
          {count}
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString('zh-CN'),
    },
  ];

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchUsers(page, userTypeFilter);
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
          <Col>
            <Title level={2} style={{ margin: 0 }}>
              <UserOutlined style={{ marginRight: '8px' }} />
              用户管理
            </Title>
          </Col>
          <Col>
            <Space>
              <Button 
                icon={<DownloadOutlined />} 
                onClick={handleExportExcel}
                loading={exportLoading}
              >
                导出Excel
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={() => fetchUsers(currentPage, userTypeFilter)}
                loading={loading}
              >
                刷新
              </Button>
              <Button 
                type="primary" 
                icon={<UserAddOutlined />}
                onClick={() => setModalVisible(true)}
              >
                新增用户
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 筛选条件 */}
        <Row style={{ marginBottom: '16px' }}>
          <Col span={6}>
            <Space>
              <span>用户类型：</span>
              <Select
                placeholder="请选择用户类型"
                allowClear
                style={{ width: 150 }}
                value={userTypeFilter}
                onChange={handleUserTypeFilterChange}
              >
                <Option value={1}>管理员</Option>
                <Option value={2}>普通用户</Option>
                <Option value={3}>体验用户</Option>
              </Select>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 800 }}
        />

        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showSizeChanger={false}
            showQuickJumper
            showTotal={(total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }
          />
        </div>
      </Card>

      {/* 新增用户弹窗 */}
      <Modal
        title="新增用户"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddUser}
          style={{ marginTop: '20px' }}
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' }
            ]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <Form.Item
            label="用户类型"
            name="user_type"
            rules={[{ required: true, message: '请选择用户类型' }]}
          >
            <Select placeholder="请选择用户类型">
              <Option value={2}>普通用户</Option>
              <Option value={3}>体验用户</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagementPage; 