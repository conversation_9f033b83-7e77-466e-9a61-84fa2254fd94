'use client'
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Tag, Typography, Space, Divider, Radio, Button, Pagination } from 'antd';
import { FireOutlined, AppstoreOutlined, BankOutlined, FilterOutlined, SearchOutlined } from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface ModelCardProps {
  id: string;
  name: string;
  provider: string;
  description: string;
  capabilities: string[];
  popular: boolean;
  type: 'audio' | 'text';
  inputPrice: string;
  outputPrice: string;
  tags: string[];
  imgSrc: string;
}

const ModelMarketPage: React.FC = () => {
  const [selectedVendor, setSelectedVendor] = useState<string>('all');
  const [selectedFunction, setSelectedFunction] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const pageSize = 6; // 每页显示的模型数量

  // 模型数据
  const modelList: ModelCardProps[] = [
    {
      id: 'dianya-model',
      name: '电牙',
      provider: '电牙',
      description: '适用于实时转写、离线转写、文本翻译、文本总结',
      capabilities: ['转写', '实时转写', '翻译', '总结'],
      popular: true,
      type: 'audio',
      inputPrice: '-',
      outputPrice: '-',
      tags: ['提供CEO级的高质量总结', '接入最多', '纯中文转写准确率99%'],
      imgSrc: '/logo/default-model-logo.png',
    },
    {
      id: 'deepseek-r1',
      name: 'deepseek-r1',
      provider: 'deepseek',
      description: '适用于文本对话',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥6.62',
      outputPrice: '￥26.50',
      tags: ['性价比高'],
      imgSrc: '/logo/deepseek-logo.svg',
    },
    {
      id: 'deepseek-v3',
      name: 'deepseek-v3',
      provider: 'deepseek',
      description: '适用于文本对话',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥3.31',
      outputPrice: '￥13.25',
      tags: ['性价比高', '稳定可靠'],
      imgSrc: '/logo/deepseek-logo.svg',
    },
    {
      id: 'gpt-3.5-turbo',
      name: 'gpt-3.5-turbo',
      provider: 'OpenAI',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥3.60',
      outputPrice: '￥10.80',
      tags: ['性价比高', '响应速度快'],
      imgSrc: '/logo/gpt-logo.png',
    },
    {
      id: 'gpt-4-turbo',
      name: 'gpt-4-turbo',
      provider: 'OpenAI',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥72.00',
      outputPrice: '￥216.00',
      tags: ['强大推理能力', '精确响应'],
      imgSrc: '/logo/gpt-logo.png',
    },
    {
      id: 'gpt-4o-mini',
      name: 'gpt-4o-mini',
      provider: 'OpenAI',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥1.08',
      outputPrice: '￥4.32',
      tags: ['入门级', '高性价比'],
      imgSrc: '/logo/gpt-logo.png',
    },
    {
      id: 'gpt-4o',
      name: 'gpt-4o',
      provider: 'OpenAI',
      description: '适用于文本对话、代码生成、多模态理解',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥18.00',
      outputPrice: '￥72.00',
      tags: ['多模态', '高性能'],
      imgSrc: '/logo/gpt-logo.png',
    },
    {
      id: 'claude-3.7-sonnet',
      name: 'claude-3.7-sonnet',
      provider: 'Anthropic',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥25.92',
      outputPrice: '￥129.60',
      tags: ['企业首选', '上下文理解能力强'],
      imgSrc: '/claude-logo.png',
    },
    {
      id: 'claude-3.5-haiku',
      name: 'claude-3.5-haiku',
      provider: 'Anthropic',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥5.76',
      outputPrice: '￥28.80',
      tags: ['响应速度快', '轻量级'],
      imgSrc: '/claude-logo.png',
    },
    {
      id: 'doubao-1.5-pro-32k',
      name: 'doubao-1.5-pro-32k',
      provider: '智谱AI',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥0.58',
      outputPrice: '￥1.44',
      tags: ['国产模型', '高性价比'],
      imgSrc: '/logo/default-model-logo.png',
    },
    {
      id: 'gemini-1.5-flash',
      name: 'gemini-1.5-flash',
      provider: 'Google',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥0.50',
      outputPrice: '￥2.02',
      tags: ['轻量级', '响应速度快'],
      imgSrc: '/gemini-logo.png',
    },
    {
      id: 'gemini-1.5-pro',
      name: 'gemini-1.5-pro',
      provider: 'Google',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥9.00',
      outputPrice: '￥36.00',
      tags: ['多模态', '推理能力强'],
      imgSrc: '/gemini-logo.png',
    },
    {
      id: 'gemini-2.0-flash',
      name: 'gemini-2.0-flash',
      provider: 'Google',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥0.72',
      outputPrice: '￥2.88',
      tags: ['最新版本', '响应速度快'],
      imgSrc: '/gemini-logo.png',
    },
    {
      id: 'grok-3',
      name: 'grok-3',
      provider: 'xAI',
      description: '适用于文本对话、代码生成',
      capabilities: ['对话'],
      popular: false,
      type: 'text',
      inputPrice: '￥21.60',
      outputPrice: '￥108.00',
      tags: ['前沿技术', '强大推理能力'],
      imgSrc: '/logo/default-model-logo.png',
    },
  ];

  // 处理模型卡片点击
  const handleModelClick = (model: ModelCardProps) => {
    if (model.type === 'audio') {
      // 触发显示dashboard事件
      const dashboardEvent = new Event('showDashboard');
      document.body.dispatchEvent(dashboardEvent);
    } else {
      // 不再通过URL参数传递模型信息，改用sessionStorage存储
      sessionStorage.setItem('selectedModel', JSON.stringify({
        id: model.id,
        name: model.name,
        provider: model.provider
      }));
      
      // 不更新URL参数
      
      // 切换到体验模型菜单
      const menuClickEvent = new CustomEvent('menuClick', {
        detail: { key: 'model-experience' }
      });
      document.body.dispatchEvent(menuClickEvent);
    }
  };

  // 厂商选项
  const vendorOptions = [
    { label: '全部', value: 'all' },
    { label: '电牙', value: '电牙' },
    { label: 'ChatGPT', value: 'OpenAI' },
    { label: 'Deepseek', value: 'deepseek' },
    { label: 'Claude', value: 'Anthropic' },
    { label: 'Gemini', value: 'Google' },
    { label: '智谱AI', value: '智谱AI' },
    { label: 'xAI', value: 'xAI' },
  ];

  // 功能选项
  const functionOptions = [
    { label: '全部', value: 'all' },
    { label: '转写', value: '转写' },
    { label: '实时转写', value: '实时转写' },
    { label: '翻译', value: '翻译' },
    { label: '总结', value: '总结' },
    { label: '对话', value: '对话' },
  ];

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 滚动到页面顶部
    window.scrollTo(0, 0);
  };

  // 根据筛选条件过滤模型
  const filteredModels = modelList.filter(model => {
    const vendorMatch = selectedVendor === 'all' || model.provider === selectedVendor;
    const functionMatch = selectedFunction === 'all' || model.capabilities.includes(selectedFunction);
    return vendorMatch && functionMatch;
  });

  // 分页显示模型
  const paginatedModels = filteredModels.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // 检查筛选后的结果是否导致当前页码无效
  useEffect(() => {
    const maxPage = Math.max(1, Math.ceil(filteredModels.length / pageSize));
    if (currentPage > maxPage) {
      setCurrentPage(maxPage);
    }
  }, [filteredModels.length, currentPage, pageSize]);

  return (
    <div style={{ 
      padding: '8px 24px 24px', 
      display: 'flex', 
      flexDirection: 'column', 
      height: 'calc(100vh - 50px)' // 减去顶部菜单栏高度
    }}>
      {/* 标题区域 - 高度更紧凑 */}
      <div style={{ marginBottom: '2px' }}>
        <Title level={4} style={{ marginBottom: '0', fontSize: '18px', lineHeight: '1.2' }}>模型超市</Title>
        <Divider style={{ margin: '2px 0 6px' }} />
      </div>

      {/* 筛选区域 - 优化为卡片式布局 */}
      <Card 
        style={{ marginBottom: '12px', borderRadius: '8px' }}
        bodyStyle={{ padding: '12px' }}
      >
        <Row gutter={[24, 12]}>
          {/* 模型功能筛选 */}
          <Col span={24}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                marginRight: '16px', 
                minWidth: '80px',
                fontWeight: 500,
                fontSize: '14px'
              }}>
                <AppstoreOutlined style={{ marginRight: '8px', color: '#1677ff' }} />
                模型功能
              </div>
              <Radio.Group 
                options={functionOptions} 
                onChange={e => {
                  setSelectedFunction(e.target.value);
                  setCurrentPage(1);
                }} 
                value={selectedFunction}
                optionType="button"
                buttonStyle="solid"
                style={{ flex: 1 }}
                size="middle"
              />
            </div>
          </Col>
          
          {/* 厂商筛选 */}
          <Col span={24}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                marginRight: '16px', 
                minWidth: '80px',
                fontWeight: 500,
                fontSize: '14px'
              }}>
                <BankOutlined style={{ marginRight: '8px', color: '#1677ff' }} />
                模型厂商
              </div>
              <Radio.Group 
                options={vendorOptions} 
                onChange={e => {
                  setSelectedVendor(e.target.value);
                  setCurrentPage(1);
                }} 
                value={selectedVendor}
                optionType="button"
                buttonStyle="solid"
                style={{ flex: 1 }}
                size="middle"
              />
            </div>
          </Col>
        </Row>
      </Card>

      {/* 筛选结果统计 */}
      <div style={{ 
        marginBottom: '10px',
        display: 'flex',
        alignItems: 'center',
        fontSize: '14px',
      }}>
        <FilterOutlined style={{ marginRight: '8px', color: '#1677ff' }} />
        <span>筛选结果：共 <Text strong>{filteredModels.length}</Text> 个模型</span>
      </div>

      {/* 模型卡片列表 - 减少卡片间距 */}
      <div style={{ flex: '1 0 auto', marginBottom: '12px' }}>
        <Row gutter={[16, 16]}>
          {paginatedModels.map(model => (
            <Col xs={24} sm={12} lg={8} key={model.id}>
              <Card 
                hoverable
                onClick={() => handleModelClick(model)}
                bodyStyle={{ padding: '16px', height: '100%', display: 'flex', flexDirection: 'column' }}
                style={{ height: '190px' }}
              >
                {model.popular && (
                  <div style={{ 
                    position: 'absolute', 
                    top: 0, 
                    right: 0, 
                    background: '#ff4d4f', 
                    color: 'white', 
                    padding: '0 8px', 
                    borderRadius: '0 0 0 4px',
                    zIndex: 1,
                    fontSize: '13px'
                  }}>
                    <FireOutlined /> 热门
                  </div>
                )}
                
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '6px' }}>
                  <img 
                    alt={model.name} 
                    src={model.imgSrc} 
                    style={{ height: '32px', width: '32px', objectFit: 'contain', marginRight: '8px' }}
                    onError={(e) => {
                      e.currentTarget.src = '/logo/default-model-logo.png'; // 默认图片
                    }}
                  />
                  <div>
                    <Title level={5} style={{ margin: 0, fontSize: '16px' }}>
                      {model.name}
                    </Title>
                    <Text type="secondary" style={{ fontSize: '13px' }}>
                      {model.provider}
                    </Text>
                  </div>
                </div>
                
                <Paragraph 
                  ellipsis={{ rows: 2 }}
                  style={{ marginBottom: '6px', fontSize: '14px' }}
                >
                  {model.description}
                </Paragraph>
                
                <div style={{ marginBottom: '4px' }}>
                  <Text type="secondary" style={{ fontSize: '13px' }}>
                    输入：{model.inputPrice}
                    {model.outputPrice !== '—' && <>   输出：{model.outputPrice}</>}
                  </Text>
                </div>
                
                <div style={{ marginTop: 'auto' }}>
                  {model.tags.map((tag, index) => (
                    <Tag key={index} color={index === 0 ? 'blue' : 'default'} style={{ marginBottom: '3px', fontSize: '12px', padding: '0 4px' }}>
                      {tag}
                    </Tag>
                  ))}
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 分页组件 - 固定在底部，无背景色，居中并放大 */}
      <div style={{ 
        padding: '10px 0', 
        display: 'flex',
        justifyContent: 'center',
        borderTop: '1px solid #f0f0f0'
      }}>
        <Pagination 
          current={currentPage} 
          pageSize={pageSize} 
          total={filteredModels.length} 
          onChange={handlePageChange}
          showSizeChanger={false}
          hideOnSinglePage={false}
          size="default"
          style={{ fontSize: '16px' }}
        />
        <style jsx global>{`
          .ant-pagination li {
            padding-left: 0 !important;
          }
        `}</style>
      </div>
    </div>
  );
};

export default ModelMarketPage; 