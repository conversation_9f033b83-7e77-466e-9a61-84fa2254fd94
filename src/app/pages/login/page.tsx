'use client'
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Form, Input, Button, message, Typography, Checkbox, Divider, Space, App, Modal } from 'antd';
import { UserOutlined, LockOutlined, CopyrightOutlined } from '@ant-design/icons';
import { loginApi } from '@/services/api';

const { Title, Text } = Typography;
const { success, error } = message;

interface LoginFormValues {
  username: string;
  password: string;
  remember?: boolean;
}

const LoginPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const currentYear = new Date().getFullYear();
  const { message } = App.useApp();
  const [contactModalVisible, setContactModalVisible] = useState(false);

  const handleSubmit = async (values: LoginFormValues) => {
    setLoading(true);
    
    try {
      const response = await loginApi({
        username: values.username,
        password: values.password,
      });
      
      if (response.success || response.code === 200) {
        message.success('登录成功');
        // 存储token
        if (response.data?.access_token) {
          localStorage.setItem('dy-token', response.data.access_token);
          localStorage.setItem('token_type', response.data.token_type);
          
          // 存储用户类型
          if (response.data.user_type !== undefined) {
            localStorage.setItem('user_type', String(response.data.user_type));
          }
          
          // 刷新当前页面，让主页判断登录状态
          window.location.reload();
        }
      } else {
        message.error(response.message || '用户名或密码错误');
      }
    } catch (err) {
      console.error('登录失败:', err);
      message.error('登录失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen bg-slate-50">
      <div className="hidden md:flex md:w-1/2 bg-slate-50 items-center justify-center">
        <div style={{ padding: '32px', maxWidth: '480px' }}>
          <img src="/logo.svg" alt="Logo" style={{ height: '64px', marginBottom: '24px' }} />
          <Title level={2} style={{ color: '#333', marginBottom: '24px' }}>智能语音转写与内容总结系统</Title>
          <div style={{ color: '#666', fontSize: '16px', lineHeight: '1.8' }}>
            本系统提供高精度的实时语音转写服务，并能智能生成内容总结。支持会议记录、采访整理、讲座转写等场景，让您轻松获取文字内容并快速掌握要点。以AI驱动的转写和总结能力，提升您的信息处理效率。
          </div>
        </div>
      </div>
      
      <div className="w-full md:w-1/2 flex items-center justify-center">
        <div style={{ width: '100%', maxWidth: '380px', padding: '32px' }}>
          <div className="flex justify-center md:hidden mb-6">
            <img src="/logo.svg" alt="Logo" style={{ height: '64px' }} />
          </div>
          
          <Title level={2} style={{ textAlign: 'center', marginBottom: '40px' }}>
            账户登录
          </Title>
          
          <Form
            name="login"
            initialValues={{ remember: true }}
            onFinish={handleSubmit}
            size="large"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名!' }]}
            >
              <Input 
                prefix={<UserOutlined className="text-gray-400" />} 
                placeholder="用户名" 
                size="large"
              />
            </Form.Item>
            
            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码!' }]}
            >
              <Input.Password 
                prefix={<LockOutlined className="text-gray-400" />} 
                placeholder="密码" 
                size="large"
              />
            </Form.Item>
            
            <Form.Item>
              <div className="flex justify-between items-center">
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox>自动登录</Checkbox>
                </Form.Item>
              </div>
            </Form.Item>
            
            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading} 
                block
                size="large"
                style={{ backgroundColor: '#FFCE3A', borderColor: '#FFCE3A', color: '#000000', fontWeight: '500', height: '40px' }}
              >
                登录
              </Button>
            </Form.Item>
          </Form>
          
          <Divider style={{ margin: '24px 0 16px' }}>
            <Text type="secondary" style={{ fontSize: '14px' }}>其他选项</Text>
          </Divider>
          
          <div className="flex justify-center">
            <Button 
              type="text" 
              onClick={() => setContactModalVisible(true)}
              style={{ color: '#666' }}
            >
              联系我们
            </Button>
          </div>
          
          <div className="text-center mt-8">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <CopyrightOutlined /> {currentYear} 改变世界（深圳）人工智能科技有限公司 版权所有
            </Text>
          </div>
        </div>
      </div>

      {/* 联系我们弹窗 */}
      <Modal
        title="联系我们"
        open={contactModalVisible}
        onCancel={() => setContactModalVisible(false)}
        footer={null}
        centered
      >
        <div style={{ padding: '20px 0' }}>
          <p><strong>公司名称：</strong>改变世界（深圳）人工智能科技有限公司</p>
          <p><strong>联系电话：</strong>0755-12345678</p>
          <p><strong>电子邮箱：</strong><EMAIL></p>
        </div>
      </Modal>
    </div>
  );
};

export default LoginPage; 