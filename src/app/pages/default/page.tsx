'use client'
import React from 'react';
import { Result, Typography } from 'antd';
import { AppstoreAddOutlined } from '@ant-design/icons';

const { Paragraph } = Typography;

const DefaultPage: React.FC = () => {
  return (
    <div style={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column', 
      justifyContent: 'center', 
      alignItems: 'center',
      padding: '40px'
    }}>
      <Result
        icon={<AppstoreAddOutlined style={{ color: '#1890ff', fontSize: '64px' }} />}
        title="功能即将上线"
        subTitle="我们正在努力开发这项功能，敬请期待！"
        extra={
          <Paragraph style={{ textAlign: 'center', maxWidth: '500px', fontSize: '16px' }}>
            感谢您对我们产品的支持和关注。我们的开发团队正在加班加点完善此功能，
            希望能尽快为您带来更好的体验。
          </Paragraph>
        }
      />
    </div>
  );
};

export default DefaultPage; 