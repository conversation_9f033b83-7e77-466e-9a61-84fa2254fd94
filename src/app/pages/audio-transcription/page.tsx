'use client'
import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Layout, message, Typography, Button, Modal} from 'antd';
import { ExclamationCircleFilled, FileTextOutlined } from '@ant-design/icons';
import AudioTranscriber, { TranscriptData, TranscriptMessage } from './AudioTranscriber';
import type { UploadFile, UploadProps } from 'antd';
import { formatDateTime } from './utils';
import type { Conversation, TabType, TranscriptionListResponse, TranscriptionDetailResponse } from './types';
import { jsPDF } from 'jspdf';
import { API_CONFIG, API_ENDPOINTS } from '@/config/api';
import { authenticatedApiRequest } from '@/utils/apiInterceptor';

const { Content } = Layout;
const { Title, Text } = Typography;

// 定义主题色
const primaryColor = 'rgba(255, 206, 57, 1)';
const primaryColorLight = 'rgba(255, 206, 57, 0.1)';
const primaryColorMedium = 'rgba(255, 206, 57, 0.6)';

// 导入组件
import SideMenu from './components/SideMenu';
import RecordingPanel from './components/RecordingPanel';
import UploadPanel from './components/UploadPanel';
import SummaryPanel from './components/SummaryPanel';
import PageHeader from './components/PageHeader';


// 为全局变量添加类型声明
declare global {
  interface Window {
    __LATEST_ACTIVE_CONVERSATION: string;
    __AUTO_SCROLL_ENABLED: boolean;
    __FONTS_PRELOADED: boolean;
    __FONT_CACHE: {
      normal?: string;
      bold?: string;
    };
    __FONT_LOADING_IN_PROGRESS: boolean;
  }
}

const ModelExperiencePage = () => {
  // 使用 message hooks
  const [messageApi, contextHolder] = message.useMessage();
  
  // 音频转写器实例
  const transcriber = useRef<AudioTranscriber | null>(null);
  
  // 状态
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordTime, setRecordTime] = useState<string>('00:00');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [processingStatus, setProcessingStatus] = useState<{
    message: string;
    type: 'processing' | 'success' | 'error';
  } | null>(null);
  const [generatingSummary, setGeneratingSummary] = useState<boolean>(() => {
    // 从localStorage恢复总结生成状态
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('summaryGeneratingState');
      if (savedState) {
        try {
          const state = JSON.parse(savedState);
          // 检查状态是否过期（超过30分钟则认为过期）
          const now = Date.now();
          if (now - state.timestamp < 30 * 60 * 1000) {
            console.log('恢复总结生成状态:', state);
            return state.isGenerating;
          } else {
            // 清除过期状态
            localStorage.removeItem('summaryGeneratingState');
          }
        } catch (error) {
          console.error('解析总结生成状态失败:', error);
          localStorage.removeItem('summaryGeneratingState');
        }
      }
    }
    return false;
  });
  const [wasHidden, setWasHidden] = useState<boolean>(false);
  // 添加自动滚动控制状态
  const [shouldAutoScroll, setShouldAutoScroll] = useState<boolean>(true);

  // 保存总结生成状态到localStorage
  const saveSummaryGeneratingState = (isGenerating: boolean, recordId?: number) => {
    if (typeof window !== 'undefined') {
      if (isGenerating) {
        const state = {
          isGenerating: true,
          recordId: recordId,
          timestamp: Date.now()
        };
        localStorage.setItem('summaryGeneratingState', JSON.stringify(state));
        console.log('保存总结生成状态到localStorage:', state);
      } else {
        localStorage.removeItem('summaryGeneratingState');
        console.log('清除总结生成状态');
      }
    }
  };

  // 获取保存的总结生成状态
  const getSavedSummaryGeneratingState = () => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('summaryGeneratingState');
      if (savedState) {
        try {
          const state = JSON.parse(savedState);
          // 检查状态是否过期（超过30分钟则认为过期）
          const now = Date.now();
          if (now - state.timestamp < 30 * 60 * 1000) {
            return state;
          } else {
            // 清除过期状态
            localStorage.removeItem('summaryGeneratingState');
          }
        } catch (error) {
          console.error('解析总结生成状态失败:', error);
          localStorage.removeItem('summaryGeneratingState');
        }
      }
    }
    return null;
  };
  // 添加一个ref来引用转写容器
  const transcriptContainerRef = useRef<HTMLDivElement | null>(null);
  
  // 添加一个用于跟踪已保存会话的状态
  const [savedConversationKeys, setSavedConversationKeys] = useState<Set<string>>(new Set());
  // 使用ref保存savedConversationKeys的当前值，确保回调函数总是能获取最新值
  const savedConversationKeysRef = useRef<Set<string>>(new Set());
  
  // 添加保存状态跟踪，防止重复保存
  const [savingInProgress, setSavingInProgress] = useState<Set<string>>(new Set());
  const savingInProgressRef = useRef<Set<string>>(new Set());
  
  // 当前活动选项卡
  const [activeTab, setActiveTab] = useState<TabType>('recording');
  
  // 添加一个useRef保存最新的conversations状态
  const conversationsRef = useRef<Conversation[]>([]);
  // 添加一个ref保存当前活动会话的ID
  const activeConversationRef = useRef<string>('');
  
  // 添加字体预加载状态
  const [fontsPreloaded, setFontsPreloaded] = useState<boolean>(false);
  
  // 添加解码进度状态
  const [decodeStatus, setDecodeStatus] = useState<string>('');
  const [decodeProgress, setDecodeProgress] = useState<number>(0);
  
  // 添加录音结束等待蒙层状态
  const [isWaitingForResult, setIsWaitingForResult] = useState<boolean>(false);
  const [waitingProgress, setWaitingProgress] = useState<number>(0);
  const [waitingMessage, setWaitingMessage] = useState<string>('正在等待服务器返回最终结果...');
  
  // 添加测试网页提示弹窗状态
  const [testWarningModalOpen, setTestWarningModalOpen] = useState<boolean>(() => {
    // 从本地存储读取同意状态，如果已同意则不显示弹窗
    if (typeof window !== 'undefined') {
      const agreed = localStorage.getItem('testWarningAgreed');
      return agreed !== 'true';
    }
    return true;
  });
  const [pageAccessible, setPageAccessible] = useState<boolean>(() => {
    // 从本地存储读取同意状态
    if (typeof window !== 'undefined') {
      const agreed = localStorage.getItem('testWarningAgreed');
      return agreed === 'true';
    }
    return false;
  });
  
  // 初始化字体缓存
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.__FONT_CACHE) {
      window.__FONT_CACHE = {};
    }

    // 组件卸载时停止录音
    return () => {
      if (transcriber.current && transcriber.current.isCurrentlyRecording()) {
        console.log('组件卸载，停止录音');
        // 组件卸载时使用简单的停止，因为页面即将关闭，复杂的保存操作可能无法完成
        // 依靠容错机制中的紧急保存和页面卸载监听器来保护数据
        transcriber.current.stopRecording();
      }
    };
  }, []);
  
  // 加载字体文件为base64的辅助函数 - 使用分片处理避免阻塞主线程
  const loadFontAsBase64 = async (fontPath: string): Promise<string> => {
    try {
      console.log(`开始后台加载字体: ${fontPath}`);
      const response = await fetch(fontPath);
      if (!response.ok) {
        throw new Error(`Failed to fetch font: ${response.status}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      // 使用分片处理，避免阻塞主线程
      const chunkSize = 8192; // 8KB 分片
      let binary = '';
      
      for (let i = 0; i < uint8Array.byteLength; i += chunkSize) {
        const chunk = uint8Array.slice(i, i + chunkSize);
        let chunkBinary = '';
        for (let j = 0; j < chunk.length; j++) {
          chunkBinary += String.fromCharCode(chunk[j]);
        }
        binary += chunkBinary;
        
        // 每处理一个分片后让出控制权，避免阻塞UI
        if (i % (chunkSize * 10) === 0) { // 每处理10个分片(约80KB)后暂停
          await new Promise(resolve => setTimeout(resolve, 0));
        }
      }
      
      console.log(`字体文件 ${fontPath} 转换完成，大小: ${binary.length} 字符`);
      return btoa(binary);
    } catch (error) {
      console.error(`加载字体文件失败 ${fontPath}:`, error);
      throw error;
    }
  };
  
  // 预加载字体文件的函数 - 完全异步，不阻塞界面
  const preloadFonts = async (retryCount = 0) => {
    // 如果已经在预加载中，避免重复启动
    if (window.__FONT_LOADING_IN_PROGRESS) {
      return;
    }
    
    if (window.__FONTS_PRELOADED && window.__FONT_CACHE.normal && window.__FONT_CACHE.bold) {
      setFontsPreloaded(true);
      return;
    }
    
    const maxRetries = 2; // 最大重试次数
    
    // 标记正在加载
    window.__FONT_LOADING_IN_PROGRESS = true;
    
    try {
      console.log(`开始后台预加载字体文件... (尝试 ${retryCount + 1}/${maxRetries + 1})`);
      
      // 使用 Promise.allSettled 并发加载，但每个加载过程都是非阻塞的
      const fontPromises = [
        loadFontAsBase64('fonts/msyh.ttf').then(base64 => {
          window.__FONT_CACHE.normal = base64;
        }).catch(error => {
          console.warn('预加载普通字体失败:', error);
          throw error;
        }),
        
        loadFontAsBase64('fonts/msyh-bold.ttf').then(base64 => {
          window.__FONT_CACHE.bold = base64;
        }).catch(error => {
          console.warn('预加载粗体字体失败:', error);
          throw error;
        })
      ];
      
      // 等待所有字体加载完成，但不阻塞界面
      const fontResults = await Promise.allSettled(fontPromises);
      
      // 检查预加载结果
      const successCount = fontResults.filter(result => result.status === 'fulfilled').length;
      const failureCount = fontResults.filter(result => result.status === 'rejected').length;
      
      console.log(`字体后台预加载结果: ${successCount} 成功, ${failureCount} 失败`);
      
      // 如果至少有一个字体加载成功，或者已经重试过，则标记为完成
      if (successCount > 0 || retryCount >= maxRetries) {
        // 标记字体已预加载
        window.__FONTS_PRELOADED = true;
        setFontsPreloaded(true);
      } else {
        // 如果所有字体都加载失败且还有重试机会，则重试
        throw new Error('所有字体加载失败');
      }
      
    } catch (error) {
      console.warn(`字体后台预加载失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);
      
      // 如果还有重试机会，则延迟后重试
      if (retryCount < maxRetries) {
        console.log(`将在 ${(retryCount + 1) * 1000}ms 后重试...`);
        setTimeout(() => {
          window.__FONT_LOADING_IN_PROGRESS = false; // 重置加载标记
          preloadFonts(retryCount + 1);
        }, (retryCount + 1) * 1000); // 递增延迟时间
      } else {
        // 重试次数用完，标记为完成（即使失败）
        console.warn('字体后台预加载重试次数用完，将使用默认字体');
        setFontsPreloaded(true);
        // 不设置 window.__FONTS_PRELOADED = true，这样PDF导出时会知道字体未成功预加载
      }
    } finally {
      // 清除加载标记
      window.__FONT_LOADING_IN_PROGRESS = false;
    }
  };
  
  // 等待字体加载完成的函数
  const waitForFontsToLoad = async (): Promise<boolean> => {
    // 如果字体已经加载完成，直接返回
    if (window.__FONTS_PRELOADED && window.__FONT_CACHE.normal && window.__FONT_CACHE.bold) {
      return true;
    }
    
    // 如果字体正在加载中，等待加载完成
    if (window.__FONT_LOADING_IN_PROGRESS) {
      
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          // 检查是否加载完成
          if (window.__FONTS_PRELOADED && window.__FONT_CACHE.normal && window.__FONT_CACHE.bold) {
            clearInterval(checkInterval);
            resolve(true);
          }
          // 检查是否加载失败或停止
          else if (!window.__FONT_LOADING_IN_PROGRESS) {
            clearInterval(checkInterval);
            resolve(false);
          }
        }, 100); // 每100ms检查一次
        
        // 设置超时，最多等待30秒
        setTimeout(() => {
          clearInterval(checkInterval);
          console.warn('等待字体加载超时');
          resolve(false);
        }, 30000);
      });
    }
    
    preloadFonts(); // 启动字体预加载
    
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        // 检查是否加载完成
        if (window.__FONTS_PRELOADED && window.__FONT_CACHE.normal && window.__FONT_CACHE.bold) {
          clearInterval(checkInterval);
          console.log('字体加载完成');
          resolve(true);
        }
        // 检查是否加载失败或停止
        else if (!window.__FONT_LOADING_IN_PROGRESS && fontsPreloaded) {
          clearInterval(checkInterval);
          console.log('字体加载已完成（可能部分失败）');
          resolve(false);
        }
      }, 100); // 每100ms检查一次
      
      // 设置超时，最多等待30秒
      setTimeout(() => {
        clearInterval(checkInterval);
        console.warn('等待字体加载超时');
        resolve(false);
      }, 30000);
    });
  };
  
  // 统一的字体设置函数
  const setupFontsForPDF = (doc: jsPDF) => {
    try {
      if (window.__FONTS_PRELOADED && window.__FONT_CACHE.normal && window.__FONT_CACHE.bold) {
        // 使用缓存的字体数据
        doc.addFileToVFS('msyh.ttf', window.__FONT_CACHE.normal);
        doc.addFont('msyh.ttf', 'msyh', 'normal');
        
        doc.addFileToVFS('msyh-bold.ttf', window.__FONT_CACHE.bold);
        doc.addFont('msyh-bold.ttf', 'msyh', 'bold');
        
        doc.setFont('msyh');
        console.log('使用预加载的缓存字体');
        return true;
      } else {
        console.warn('字体未预加载或缓存不完整，使用默认字体');
        return false;
      }
    } catch (error) {
      console.warn('设置字体失败，使用默认字体:', error);
      return false;
    }
  };
  
  // 在组件挂载时预加载字体 - 使用更异步的方式
  useEffect(() => {
    // 使用 requestIdleCallback 在浏览器空闲时预加载字体，如果不支持则使用 setTimeout
    const schedulePreload = () => {
      if (typeof window !== 'undefined') {
        if ('requestIdleCallback' in window) {
          // 使用 requestIdleCallback 在浏览器空闲时执行
          (window as any).requestIdleCallback(() => {
            preloadFonts();
          }, { timeout: 2000 }); // 最多等待2秒
        } else {
          // 降级到 setTimeout，延迟执行避免阻塞初始化
          setTimeout(() => {
            preloadFonts();
          }, 500); // 减少延迟时间，但仍然避免阻塞初始化
        }
      }
    };
    
    // 立即调度预加载任务
    schedulePreload();
    
    return () => {
      // 组件卸载时清理加载状态
      if (typeof window !== 'undefined') {
        window.__FONT_LOADING_IN_PROGRESS = false;
      }
    };
  }, []);
  
  // 优化字体预加载状态指示器的显示逻辑
  const [showFontIndicator, setShowFontIndicator] = useState<boolean>(false);
  
  // 监听字体预加载状态，控制指示器显示
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // 如果字体还未预加载且不在加载中，显示指示器
      const shouldShow = !fontsPreloaded && !window.__FONTS_PRELOADED;
      setShowFontIndicator(shouldShow);
      
      // 如果字体预加载完成，延迟隐藏指示器
      if (fontsPreloaded) {
        const timer = setTimeout(() => {
          setShowFontIndicator(false);
        }, 1000); // 1秒后隐藏
        
        return () => clearTimeout(timer);
      }
    }
  }, [fontsPreloaded]);
  
  // 当conversations变化时更新ref
  useEffect(() => {
    conversationsRef.current = conversations;
  }, [conversations]);
  
  // 当savedConversationKeys变化时更新ref
  useEffect(() => {
    savedConversationKeysRef.current = savedConversationKeys;
  }, [savedConversationKeys]);
  
  // 当savingInProgress变化时更新ref
  useEffect(() => {
    savingInProgressRef.current = savingInProgress;
  }, [savingInProgress]);
  
  // 当activeConversation变化时更新ref
  useEffect(() => {
    activeConversationRef.current = activeConversation;
  }, [activeConversation]);

  // 安全文件验证功能
  const AUDIO_MAGIC_NUMBERS = {
    'mp3': [0x49, 0x44, 0x33], // ID3
    'mp3_alt': [0xFF, 0xFB], // MPEG-1 Layer 3
    'mp3_alt2': [0xFF, 0xF3], // MPEG-1 Layer 3
    'mp3_alt3': [0xFF, 0xF2], // MPEG-1 Layer 3
    'mp3_alt4': [0xFF, 0xFA], // MPEG-1 Layer 2
    'mp3_alt5': [0xFF, 0xE2], // MPEG-2 Layer 3
    'mp3_alt6': [0xFF, 0xE3], // MPEG-2 Layer 3
    'mp3_alt7': [0xFF, 0xDA], // MPEG-2.5 Layer 3
    'mp3_alt8': [0xFF, 0xDB], // MPEG-2.5 Layer 3
    'mp3_alt9': [0xFF, 0xF4], // MPEG-1 Layer 1
    'mp3_alt10': [0xFF, 0xFC], // MPEG-1 Layer 1
    'mp3_alt11': [0xFF, 0xE4], // MPEG-2 Layer 1
    'mp3_alt12': [0xFF, 0xEC], // MPEG-2 Layer 1
    'mp3_alt13': [0xFF, 0xDC], // MPEG-2.5 Layer 1
    'mp3_alt14': [0xFF, 0xD4], // MPEG-2.5 Layer 2
    'wav': [0x52, 0x49, 0x46, 0x46], // RIFF
    // AAC ADTS格式的各种变体（增强覆盖）
    'aac': [0xFF, 0xF0], // AAC ADTS
    'aac_alt': [0xFF, 0xF1], // AAC ADTS
    'aac_alt2': [0xFF, 0xF8], // AAC ADTS
    'aac_alt3': [0xFF, 0xF9], // AAC ADTS
    'aac_alt4': [0xFF, 0xFA], // AAC ADTS
    'aac_alt5': [0xFF, 0xFC], // AAC ADTS
    'aac_alt6': [0xFF, 0xFD], // AAC ADTS
    'aac_alt7': [0xFF, 0xFE], // AAC ADTS
    'aac_alt8': [0xFF, 0xFF], // AAC ADTS
    // MP4/M4A容器中的AAC
    'aac_mp4': [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70, 0x4D, 0x34, 0x41], // ftyp M4A
    'aac_mp4_alt': [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70, 0x4D, 0x34, 0x41], // ftyp M4A
    'm4a': [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70], // ftyp
    'm4a_alt': [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70], // ftyp
    'ogg': [0x4F, 0x67, 0x67, 0x53], // OggS
    'flac': [0x66, 0x4C, 0x61, 0x43], // fLaC
    'wma': [0x30, 0x26, 0xB2, 0x75, 0x8E, 0x66, 0xCF, 0x11], // WMA
    'amr': [0x23, 0x21, 0x41, 0x4D, 0x52], // #!AMR
    'mp4': [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70], // ftyp (mp4)
    'mp4_alt': [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70] // ftyp (mp4)
  };

  const ALLOWED_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.mp4', '.wma', '.ogg', '.amr', '.flac'];

  const validateFileName = (fileName: string): { isValid: boolean; error?: string } => {
    if (!fileName || typeof fileName !== 'string') {
      return { isValid: false, error: '文件名无效' };
    }
    
    // 检查文件名长度
    if (fileName.length > 255) {
      return { isValid: false, error: '文件名过长' };
    }
    
    // 检查是否包含危险字符
    const dangerousChars = /[<>:"|?*\x00-\x1F]/;
    if (dangerousChars.test(fileName)) {
      return { isValid: false, error: '文件名包含非法字符' };
    }
    
    // 检查是否以点开头（隐藏文件）
    if (fileName.startsWith('.')) {
      return { isValid: false, error: '不允许上传隐藏文件' };
    }
    
    // 获取所有扩展名（处理双扩展名）
    const parts = fileName.toLowerCase().split('.');
    if (parts.length < 2) {
      return { isValid: false, error: '文件必须有扩展名' };
    }
    
    // 检查是否存在多个扩展名（双扩展名攻击）
    if (parts.length > 2) {
      // 检查是否有可执行文件扩展名
      const executableExtensions = [
        'exe', 'bat', 'cmd', 'com', 'scr', 'pif', 'vbs', 'js', 'jar', 
        'msi', 'ps1', 'sh', 'py', 'php', 'asp', 'aspx', 'jsp'
      ];
      
      // 检查是否有音频格式混用（防止 .mp3.aac 这种攻击）
      const audioExtensions = ['mp3', 'wav', 'm4a', 'aac', 'mp4', 'wma', 'ogg', 'amr', 'flac'];
      
      for (let i = 1; i < parts.length - 1; i++) {
        const currentExt = parts[i];
        
        // 检测可执行文件扩展名
        if (executableExtensions.includes(currentExt)) {
          return { isValid: false, error: '检测到可疑的文件扩展名' };
        }
        
        // 检测音频格式混用（如 test.mp3.aac）
        if (audioExtensions.includes(currentExt)) {
          return { isValid: false, error: '检测到多重音频格式扩展名，可能是恶意文件' };
        }
        
        // 检测其他可疑扩展名模式
        if (currentExt.length >= 2) {
          return { isValid: false, error: '文件名包含多个扩展名，不允许上传' };
        }
      }
    }
    
    // 验证最终扩展名
    const finalExtension = '.' + parts[parts.length - 1];
    if (!ALLOWED_EXTENSIONS.includes(finalExtension)) {
      return { isValid: false, error: `不支持的文件格式: ${finalExtension}` };
    }
    
    return { isValid: true };
  };

  const validateFileHeader = async (file: File): Promise<{ isValid: boolean; error?: string }> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      
      reader.onload = function(e) {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          if (!arrayBuffer) {
            resolve({ isValid: false, error: '无法读取文件内容' });
            return;
          }
          
          const bytes = new Uint8Array(arrayBuffer);
          let detectedFormat = null;
          let isValidHeader = false;
          
          // 检查各种音频文件的魔数，同时记录检测到的格式
          for (const [format, magicNumbers] of Object.entries(AUDIO_MAGIC_NUMBERS)) {
            if (bytes.length >= magicNumbers.length) {
              const matches = magicNumbers.every((byte, index) => bytes[index] === byte);
              if (matches) {
                detectedFormat = format;
                isValidHeader = true;
                break;
              }
            }
          }
          
          // 如果检测到文件头，验证格式与扩展名是否匹配
          if (isValidHeader && detectedFormat) {
            const fileName = file.name.toLowerCase();
            const fileExtension = fileName.split('.').pop();
            
                         // 定义格式映射关系
             const formatMapping: { [key: string]: string[] } = {
               'mp3': ['mp3'],
               'mp3_alt': ['mp3'],
               'mp3_alt2': ['mp3'],
               'mp3_alt3': ['mp3'],
               'mp3_alt4': ['mp3'],
               'mp3_alt5': ['mp3'],
               'mp3_alt6': ['mp3'],
               'mp3_alt7': ['mp3'],
               'mp3_alt8': ['mp3'],
               'mp3_alt9': ['mp3'],
               'mp3_alt10': ['mp3'],
               'mp3_alt11': ['mp3'],
               'mp3_alt12': ['mp3'],
               'mp3_alt13': ['mp3'],
               'mp3_alt14': ['mp3'],
              'wav': ['wav'],
              'aac': ['aac'],
              'aac_alt': ['aac'],
              'aac_alt2': ['aac'],
              'aac_alt3': ['aac'],
              'aac_alt4': ['aac'],
              'aac_alt5': ['aac'],
              'aac_alt6': ['aac'],
              'aac_alt7': ['aac'],
              'aac_alt8': ['aac'],
              'aac_mp4': ['aac', 'm4a'],
              'aac_mp4_alt': ['aac', 'm4a'],
              'm4a': ['m4a', 'mp4'],
              'm4a_alt': ['m4a', 'mp4'],
              'mp4': ['mp4', 'm4a'],
              'mp4_alt': ['mp4', 'm4a'],
              'ogg': ['ogg'],
              'flac': ['flac'],
              'wma': ['wma'],
              'amr': ['amr']
            };
            
            const expectedExtensions = formatMapping[detectedFormat];
            if (!expectedExtensions || !expectedExtensions.includes(fileExtension || '')) {
              console.warn(`文件头格式不匹配：检测到${detectedFormat}格式，但扩展名是.${fileExtension}`);
              resolve({ 
                isValid: false, 
                error: `文件格式验证失败：检测到${detectedFormat.includes('mp4') ? 'MP4' : detectedFormat.includes('mp3') ? 'MP3' : detectedFormat.toUpperCase()}格式文件，但扩展名是.${fileExtension}，可能是伪装攻击` 
              });
              return;
            }
            
            console.log(`文件头验证通过：${detectedFormat}格式匹配.${fileExtension}扩展名`);
          }
          
          // 如果标准魔数检测失败，但文件扩展名是.mp3，进行额外验证
          if (!isValidHeader && file.name.toLowerCase().endsWith('.mp3')) {
            console.log('开始MP3文件额外验证，文件名:', file.name);
            console.log('文件大小:', file.size);
            console.log('前16字节:', Array.from(bytes.slice(0, 16)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
            
            // MP3文件的额外验证逻辑
            if (bytes.length >= 2) {
              // 检查是否是MPEG同步字节的其他变体 (0xFF + second byte with specific patterns)
              if (bytes[0] === 0xFF && (bytes[1] & 0xE0) === 0xE0) {
                // 0xE0 = 11100000, 检查前3位是否为111
                console.log('MP3文件通过MPEG同步字节验证');
                isValidHeader = true;
              }
              // 检查是否可能是没有ID3标签的原始MP3文件
              else if (file.size > 100 && file.size < 200 * 1024 * 1024) {
                // 检查文件内容是否包含明显的非音频数据特征
                const firstBytes = Array.from(bytes.slice(0, Math.min(64, bytes.length)));
                
                // 检查是否包含大量可打印ASCII字符（可能是文本文件）
                const printableChars = firstBytes.filter(byte => 
                  (byte >= 32 && byte <= 126) || byte === 9 || byte === 10 || byte === 13
                );
                const printableRatio = printableChars.length / firstBytes.length;
                
                // 检查是否包含HTML/XML标签
                const fileContent = String.fromCharCode(...firstBytes.slice(0, 16));
                const hasHtmlTags = /<[^>]+>/.test(fileContent);
                
                // 检查是否包含可执行文件特征
                const hasMZHeader = bytes[0] === 0x4D && bytes[1] === 0x5A; // MZ header
                const hasELFHeader = bytes[0] === 0x7F && bytes[1] === 0x45 && bytes[2] === 0x4C && bytes[3] === 0x46; // ELF
                
                console.log('MP3额外验证详情:', {
                  printableRatio: printableRatio.toFixed(2),
                  hasHtmlTags,
                  hasMZHeader,
                  hasELFHeader,
                  firstBytesAsString: fileContent.replace(/[^\x20-\x7E]/g, '.')
                });
                
                // 如果满足以下条件，认为是有效的MP3文件：
                // 1. 可打印字符比例小于70%（二进制音频数据特征）
                // 2. 文件不包含HTML标签、可执行文件头
                // 3. 文件大小合理
                if (printableRatio < 0.7 && !hasHtmlTags && !hasMZHeader && !hasELFHeader && file.size > 1000) {
                  console.log('MP3文件通过启发式验证');
                  isValidHeader = true;
                }
              }
            }
          }
          
          // 如果标准魔数检测失败，但文件扩展名是.aac，进行额外验证
          if (!isValidHeader && file.name.toLowerCase().endsWith('.aac')) {
            console.log('开始AAC文件额外验证，文件名:', file.name);
            console.log('文件大小:', file.size);
            console.log('前16字节:', Array.from(bytes.slice(0, 16)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
            
            // AAC文件的额外验证逻辑
            if (bytes.length >= 2) {
              // 1. 检查是否是AAC syncword的变体 (0xFF Fx)
              if (bytes[0] === 0xFF && (bytes[1] & 0xF0) === 0xF0) {
                console.log('AAC文件通过syncword验证');
                isValidHeader = true;
              }
              // 2. 检查更多AAC syncword变体
              else if (bytes.length >= 4) {
                // 检查AAC-LC、HE-AAC等格式的syncword
                if ((bytes[0] === 0xFF && (bytes[1] & 0xF6) === 0xF0) || 
                    (bytes[0] === 0xFF && (bytes[1] & 0xF6) === 0xF2) ||
                    (bytes[0] === 0xFF && (bytes[1] & 0xF6) === 0xF4) ||
                    (bytes[0] === 0xFF && (bytes[1] & 0xF6) === 0xF6)) {
                  console.log('AAC文件通过扩展syncword验证');
                  isValidHeader = true;
                }
                // 3. 检查是否可能是Raw AAC或其他AAC容器格式
                else if (file.size > 100 && file.size < 200 * 1024 * 1024) {
                  // 检查文件内容是否包含明显的非音频数据特征
                  const firstBytes = Array.from(bytes.slice(0, Math.min(64, bytes.length)));
                  
                  // 检查是否包含大量可打印ASCII字符（可能是文本文件）
                  const printableChars = firstBytes.filter(byte => 
                    (byte >= 32 && byte <= 126) || byte === 9 || byte === 10 || byte === 13
                  );
                  const printableRatio = printableChars.length / firstBytes.length;
                  
                  // 检查是否包含HTML/XML标签
                  const fileContent = String.fromCharCode(...firstBytes.slice(0, 16));
                  const hasHtmlTags = /<[^>]+>/.test(fileContent);
                  
                  // 检查是否包含可执行文件特征
                  const hasMZHeader = bytes[0] === 0x4D && bytes[1] === 0x5A; // MZ header
                  const hasELFHeader = bytes[0] === 0x7F && bytes[1] === 0x45 && bytes[2] === 0x4C && bytes[3] === 0x46; // ELF
                  
                  console.log('AAC额外验证详情:', {
                    printableRatio: printableRatio.toFixed(2),
                    hasHtmlTags,
                    hasMZHeader,
                    hasELFHeader,
                    firstBytesAsString: fileContent.replace(/[^\x20-\x7E]/g, '.')
                  });
                  
                  // 如果满足以下条件，认为是有效的AAC文件：
                  // 1. 可打印字符比例小于80%（放宽条件以支持更多AAC文件）
                  // 2. 文件不包含HTML标签、可执行文件头
                  // 3. 文件大小合理
                  if (printableRatio < 0.8 && !hasHtmlTags && !hasMZHeader && !hasELFHeader && file.size > 1000) {
                    console.log('AAC文件通过启发式验证');
                    isValidHeader = true;
                  }
                }
              }
            }
          }
          
          if (!isValidHeader) {
            resolve({ isValid: false, error: '文件头验证失败，可能不是有效的音频文件' });
            return;
          }
          
          resolve({ isValid: true });
        } catch (error) {
          resolve({ isValid: false, error: '文件头验证过程中发生错误' });
        }
      };
      
      reader.onerror = function() {
        resolve({ isValid: false, error: '无法读取文件' });
      };
      
      // 只读取文件的前64字节用于头部验证
      const blob = file.slice(0, 64);
      reader.readAsArrayBuffer(blob);
    });
  };

  const validateFileSecurely = async (file: File): Promise<{ isValid: boolean; error?: string }> => {
    // 1. 基本文件对象验证
    if (!file || !(file instanceof File)) {
      return { isValid: false, error: '无效的文件对象' };
    }
    
    // 2. 文件大小验证
    const maxSize = 200 * 1024 * 1024; // 200MB
    if (file.size > maxSize) {
      return { isValid: false, error: '文件大小不能超过200MB' };
    }
    
    if (file.size === 0) {
      return { isValid: false, error: '文件大小为0，可能是空文件' };
    }
    
    // 3. 文件名验证
    const nameValidation = validateFileName(file.name);
    if (!nameValidation.isValid) {
      return nameValidation;
    }
    
    // 4. 文件头验证
    const headerValidation = await validateFileHeader(file);
    if (!headerValidation.isValid) {
      return headerValidation;
    }
    
    return { isValid: true };
  };
  
  // 页面可见性变化处理
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        console.log('页面隐藏，可能是锁屏或切换应用');
        setWasHidden(true);
        
        // 如果正在处理，显示锁屏提示
        if (isProcessing) {
          setProcessingStatus({
            message: '检测到页面隐藏，转写可能会暂停',
            type: 'processing'
          });
        }
      } else if (document.visibilityState === 'visible' && wasHidden) {
        console.log('页面从隐藏状态恢复可见');
        // 只有在之前隐藏过且正在处理时才显示提示
        if (isProcessing) {
          message.info('页面已恢复可见');
        }
        setWasHidden(false);
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [wasHidden, isProcessing]);
  
  // 滚动到最新消息的辅助函数
  const scrollToLatestMessage = () => {
    // 检查全局变量中的滚动状态，这是最可靠的方法
    const shouldScroll = typeof window.__AUTO_SCROLL_ENABLED !== 'undefined' 
      ? window.__AUTO_SCROLL_ENABLED 
      : shouldAutoScroll;
    
    if (!shouldScroll) {
      return;
    }
    
    // 使用setTimeout确保在DOM更新后再滚动
    setTimeout(() => {
      const containers = document.querySelectorAll('.transcript-container');
      containers.forEach(container => {
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    }, 100);
  };
  
  // 添加一个处理滚动事件的函数
  const handleTranscriptScroll = (event: Event) => {
    const container = event.target as HTMLElement;
    if (!container) return;
    
    // 计算是否在底部附近（减小误差值，使检测更精确）
    const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 10;
    
    // 如果用户滚动到底部，恢复自动滚动
    if (isAtBottom) {
      if (!window.__AUTO_SCROLL_ENABLED || !shouldAutoScroll) {
        // 使用全局变量立即标记自动滚动状态，以便其他函数可以立即获取
        window.__AUTO_SCROLL_ENABLED = true;
        
        // 更新React状态
        setShouldAutoScroll(true);
      }
    } 
    // 如果用户向上滚动且当前设置为自动滚动，则禁用自动滚动
    else if (window.__AUTO_SCROLL_ENABLED || shouldAutoScroll) {
      
      // 使用全局变量立即标记自动滚动状态，以便其他函数可以立即获取
      window.__AUTO_SCROLL_ENABLED = false;
      
      // 更新React状态
      setShouldAutoScroll(false);
    }
  };
  
  // 初始化转写器
  useEffect(() => {
    if (!transcriber.current) {
      console.log('初始化转写器...');
      transcriber.current = new AudioTranscriber();
      
      // 设置回调函数
      transcriber.current.setOnTranscriptUpdate((data) => {
        console.log('收到转写更新:', data);
        console.log('可能重复的id', Date.now().toString());

        // 获取当前活动会话ID
        const currentActiveConversation = window.__LATEST_ACTIVE_CONVERSATION || activeConversation;
        
        // 改进消息ID生成策略，确保唯一性
        const messageId = `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        
        // 创建新消息
        const newMessage: TranscriptMessage = {
          id: messageId,
          content: data.text,
          isUser: false,
          timestamp: Date.now(),
          speaker: data.speaker // 添加对话人信息
        };
        console.log(newMessage);
        
        // 更新会话列表
        setConversations(prevConversations => {
          
          // 再次尝试获取当前活动会话ID
          const activeConvId = currentActiveConversation || '';
          
          if (activeConvId === '') {
            // 尝试使用第一个会话
            const updatedConversations = [...prevConversations];
            if (updatedConversations.length > 0) {
              updatedConversations[0] = {
                ...updatedConversations[0],
                messages: [...updatedConversations[0].messages, newMessage]
              };
              return updatedConversations;
            }
            return prevConversations;
          }
  
          const updated = prevConversations.map(conv => {
            if (conv.key === activeConvId) {
              return {
                ...conv,
                messages: [...conv.messages, newMessage]
              };
            }
            return conv;
          });
          
          // 验证消息是否成功添加
          const targetConv = updated.find(conv => conv.key === activeConvId);
          if (targetConv) {
            // 验证新消息是否确实添加到了会话中
            const newMessageExists = targetConv.messages.some(msg => msg.id === messageId);
          } else {
            console.error('未找到目标会话进行消息添加，活动会话ID:', activeConvId);
          }
          
          // 添加消息后，仅在shouldAutoScroll为true时自动滚动到底部
          // 使用从全局变量中获取的最新状态，而不是闭包中可能过时的状态
          
          // 使用setTimeout确保在React状态更新后再检查滚动状态
          setTimeout(() => {
            // 再次检查全局变量中的滚动状态
            const shouldScroll = typeof window.__AUTO_SCROLL_ENABLED !== 'undefined' 
              ? window.__AUTO_SCROLL_ENABLED 
              : shouldAutoScroll;
              
            
            if (shouldScroll) {
              const containers = document.querySelectorAll('.transcript-container');
              containers.forEach(container => {
                if (container) {
                  container.scrollTop = container.scrollHeight;
                }
              });
            }
          }, 10);
          
          return updated;
        });
      });
      
      transcriber.current.setOnStatusChange((statusMessage, type) => {
        setProcessingStatus({ message: statusMessage, type });
        
        if (type === 'success' || type === 'error') {
          setTimeout(() => {
            setProcessingStatus(null);
          }, 3000);
        }
        
        // 处理恢复相关的状态消息
        if (statusMessage.includes('检测到连接中断') || statusMessage.includes('准备恢复')) {
          message.info('检测到连接中断，正在重新连接...');
        } else if (statusMessage.includes('已恢复录音') || statusMessage.includes('恢复处理')) {
          message.success('转写已成功恢复！');
          
          // 如果有文件正在处理，更新UI状态
          if (statusMessage.includes('恢复处理音频文件')) {
            setIsProcessing(true);
          }
          
          // 如果是恢复录音，更新录音状态
          if (statusMessage.includes('已恢复录音')) {
            setIsRecording(true);
          }
        } else if (statusMessage.includes('恢复处理失败')) {
          message.error('恢复转写失败，请重新开始');
          // 重置处理状态
          setIsProcessing(false);
          setIsRecording(false);
        }
        
        // 处理状态变更
        if (statusMessage.includes('正在处理')) {
          setIsProcessing(true);
        }
        
        if (statusMessage.includes('转写完成') || statusMessage.includes('处理音频文件时出错')) {
          setIsProcessing(false);
        }
      });
      
      transcriber.current.setOnRecordTimeUpdate((time) => {
        setRecordTime(time);
      });

      // 设置解码进度回调
      transcriber.current.setOnDecodeProgress((status, progress) => {
        console.log('解码进度:', status, progress);
        setDecodeStatus(status);
        if (progress !== undefined) {
          setDecodeProgress(progress);
        }
        
        // 如果解码完成或失败，3秒后清除状态
        if (status.includes('解码成功') || status.includes('解码失败') || status.includes('所有解码策略都失败')) {
          setTimeout(() => {
            setDecodeStatus('');
            setDecodeProgress(0);
          }, 3000);
        }
      });

      // 设置容错回调函数
      transcriber.current.setFaultToleranceCallbacks({
        onPeriodicSave: async (data) => {
          console.log('执行定期保存，数据条数:', data.length);
          await handleFaultToleranceSave(data, '定期保存');
        },
        onEmergencySave: async (data, reason) => {
          console.log('执行紧急保存，原因:', reason, '数据条数:', data.length);
          await handleFaultToleranceSave(data, reason);
        }
      });
    }
    
    // 清理函数
    return () => {
      if (transcriber.current) {
        // 使用新增的 destroy 方法清理资源
        transcriber.current.destroy();
        
        // 清理后将引用置为 null
        transcriber.current = null;
      }
    };
  }, []);

  // 立即保存当前录音记录的公共函数
  const saveCurrentRecordingImmediately = async (reason: string = '手动保存') => {
    // 使用ref获取最新的状态，避免闭包问题
    const currentConvKey = activeConversationRef.current;
    const latestConversations = conversationsRef.current;
    
    if (!currentConvKey || latestConversations.length === 0) {
      console.warn('没有活动会话，无法保存录音记录');
      return false;
    }

    // 检查是否已经在保存中，防止重复保存
    if (savingInProgressRef.current.has(currentConvKey)) {
      console.log(`会话 ${currentConvKey} 正在保存中，跳过重复保存，原因: ${reason}`);
      return true; // 返回true表示已经在处理中
    }

    const currentConv = latestConversations.find(conv => conv.key === currentConvKey);
    if (!currentConv || !currentConv.messages || currentConv.messages.length === 0) {
      console.warn('未找到当前会话或会话无内容，无法保存录音记录');
      return false;
    }

    // 检查会话是否已经保存过
    if (savedConversationKeysRef.current.has(currentConvKey)) {
      console.log(`会话 ${currentConvKey} 已保存过，跳过重复保存，原因: ${reason}`);
      return true;
    }

    try {
      console.log(`立即保存录音记录到服务器，原因: ${reason}，会话: ${currentConvKey}`);
      
      // 标记正在保存
      savingInProgressRef.current.add(currentConvKey);
      setSavingInProgress(prev => new Set(prev).add(currentConvKey));
      
      // 使用会话的serverId进行更新，如果没有则创建新记录
      const newServerId = await saveTranscriptionRecord(currentConv, 'audio', currentConv.serverId);
      
      // 如果返回了新的serverId，更新会话
      if (newServerId && !currentConv.serverId) {
        setConversations(prevConversations => {
          return prevConversations.map(conv => {
            if (conv.key === currentConvKey) {
              return {
                ...conv,
                serverId: newServerId,
                id: newServerId,
                isLocal: false // 移除本地标记，让会话显示在历史记录中
              };
            }
            return conv;
          });
        });
      }

      // 标记会话已保存
      setSavedConversationKeys(prev => new Set(prev).add(currentConvKey));

      console.log(`录音记录保存成功，原因: ${reason}，会话: ${currentConvKey}`);
      return true;
    } catch (error: any) {
      console.error(`保存录音记录失败，原因: ${reason}，错误:`, error);
      return false;
    } finally {
      // 清除保存状态
      savingInProgressRef.current.delete(currentConvKey);
      setSavingInProgress(prev => {
        const newSet = new Set(prev);
        newSet.delete(currentConvKey);
        return newSet;
      });
    }
  };

  // 容错保存处理函数
  const handleFaultToleranceSave = async (data: TranscriptData[], reason: string) => {
    try {
      console.log(`开始容错保存，原因: ${reason}，数据条数: ${data.length}`);
      
      // 获取当前活动会话
      const currentActiveConversation = window.__LATEST_ACTIVE_CONVERSATION || activeConversation;
      
      if (!currentActiveConversation) {
        console.warn('没有活动会话，无法执行容错保存');
        return;
      }
      
      // 使用conversationsRef.current而不是conversations状态，避免异步更新问题
      const currentConv = conversationsRef.current.find(conv => conv.key === currentActiveConversation);
      
      if (!currentConv) {
        console.warn('未找到当前会话，无法执行容错保存。当前会话ID:', currentActiveConversation);
        console.warn('当前会话列表:', conversationsRef.current.map(conv => ({ key: conv.key, label: conv.label })));
        return;
      }
      
      // 如果会话没有消息或消息很少，跳过保存
      if (!currentConv.messages || currentConv.messages.length < 3) {
        console.log('会话消息太少，跳过容错保存');
        return;
      }
      
      // 创建一个临时会话对象用于保存
      const tempConv = {
        ...currentConv,
        label: currentConv.label + (reason.includes('紧急') ? ' (紧急保存)' : ' (自动保存)')
      };
      
      // 调用保存函数，传入'audio'类型（因为这是实时录音转写）
      // 如果会话已有serverId，则进行更新；否则创建新记录
      const newServerId = await saveTranscriptionRecord(tempConv, 'audio', currentConv.serverId);
      
      // 更新会话状态
      setConversations(prevConversations => {
        return prevConversations.map(conv => {
          if (conv.key === currentActiveConversation) {
            if (newServerId && !currentConv.serverId) {
              // 首次保存，设置serverId和id
              return {
                ...conv,
                serverId: newServerId,
                id: newServerId,
                isLocal: false // 标记为已保存到服务器
              };
            } else {
              // 更新操作，只需要标记为已保存
              return {
                ...conv,
                isLocal: false // 标记为已保存到服务器
              };
            }
          }
          return conv;
        });
      });
      
      console.log(`容错保存完成，原因: ${reason}`);
      
      // 显示保存成功提示（仅对紧急保存显示）
      if (reason.includes('紧急') || reason.includes('错误') || reason.includes('异常')) {
        message.success(`已自动保存转写内容 (${reason})`);
      }
      
    } catch (error) {
      console.error('容错保存失败:', error);
      
      // 对于紧急保存失败，显示错误提示
      if (reason.includes('紧急') || reason.includes('错误') || reason.includes('异常')) {
        message.error(`自动保存失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }
  };
  
  // 在全局对象上存储最新的activeConversation值
  useEffect(() => {
    // 将最新的activeConversation值存储在window对象上，供回调函数访问
    window.__LATEST_ACTIVE_CONVERSATION = activeConversation;
    console.log('更新全局活动会话ID:', activeConversation);
    
    // 更新当前会话的文件列表显示
    const currentConv = conversations.find(conv => conv.key === activeConversation);
    // 🔧 修复：只有在非录音状态下才更新录音时长显示
    if (!isRecording) {
      if (currentConv?.recordingTime) {
        setRecordTime(currentConv.recordingTime);
      } else {
        setRecordTime('00:00');
      }
    }

    // 更新文件列表显示
    if (currentConv?.fileInfo) {
      // 如果当前会话有文件信息，则显示该文件
      setFileList([{
        uid: currentConv.fileInfo.uid,
        name: currentConv.fileInfo.name,
        status: 'done',
      }]);
    } else if (currentConv?.id && currentConv.messages && currentConv.messages.length > 0) {
      // 如果是从后端加载的历史记录，并且有消息内容，则创建一个假的文件信息
      setFileList([{
        uid: `history_${currentConv.id}`,
        name: currentConv.label,
        status: 'done',
      }]);
    } else {
      // 如果当前会话没有文件信息，则清空文件列表
      setFileList([]);
    }
    
    // 注意：此处移除了自动滚动到最新消息的逻辑
    // 用户点击历史记录查看详情时，不应自动滚动到底部，让用户可以从顶部开始阅读
  }, [activeConversation, conversations]);
  
  // 添加滚动事件监听器
  useEffect(() => {
    // 添加滚动事件监听器的函数
    const addScrollListener = (container: Element) => {
      // 先移除可能存在的旧监听器，避免重复
      container.removeEventListener('scroll', handleTranscriptScroll);
      // 添加新的监听器
      container.addEventListener('scroll', handleTranscriptScroll, { passive: true });
      
      // 检查初始滚动位置
      setTimeout(() => {
        const isAtBottom = (container as HTMLElement).scrollHeight - 
                           (container as HTMLElement).scrollTop - 
                           (container as HTMLElement).clientHeight < 20;
        
        // 根据初始位置设置自动滚动状态
        if (isAtBottom) {
          window.__AUTO_SCROLL_ENABLED = true;
          setShouldAutoScroll(true);
        }
      }, 300);
    };
    
    // 初始添加监听器
    const addInitialListeners = () => {
      const containers = document.querySelectorAll('.transcript-container');
      containers.forEach(container => {
        addScrollListener(container);
      });
    };
    
    // 初始延迟添加监听器，确保DOM已经渲染
    const timer = setTimeout(() => {
      addInitialListeners();
    }, 500);
    
    // 创建一个MutationObserver来监听DOM变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          // 检查是否有新的转写容器被添加
          setTimeout(() => {
            const containers = document.querySelectorAll('.transcript-container');
            containers.forEach(container => {
              // 检查容器是否已经有监听器
              if (!container.hasAttribute('data-scroll-listener')) {
                addScrollListener(container);
                // 标记容器已添加监听器
                container.setAttribute('data-scroll-listener', 'true');
              }
            });
          }, 100);
        }
      });
    });
    
    // 开始观察DOM变化
    observer.observe(document.body, { childList: true, subtree: true });
    
    // 定期检查并添加监听器，确保在组件重新渲染后仍然有效
    const intervalTimer = setInterval(() => {
      const containers = document.querySelectorAll('.transcript-container');
      containers.forEach(container => {
        if (!container.hasAttribute('data-scroll-listener')) {
          addScrollListener(container);
          container.setAttribute('data-scroll-listener', 'true');
        }
      });
    }, 2000);
    
    // 清理函数
    return () => {
      clearTimeout(timer);
      clearInterval(intervalTimer);
      observer.disconnect();
      const containers = document.querySelectorAll('.transcript-container');
      containers.forEach(container => {
        container.removeEventListener('scroll', handleTranscriptScroll);
      });
    };
  }, []);
  
  // 添加加载历史记录的函数
  const loadTranscriptionHistory = async (type: 'file' | 'audio') => {
    try {
      console.log('开始加载历史记录, 类型:', type);
      
      const response = await authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.listTranscriptions}?type=${type}`, {
        method: 'GET',
      });

      if (!response.success) {
        console.error('加载历史记录失败:', response.message);
        throw new Error(`服务器返回错误: ${response.code}, ${response.message}`);
      }

      const data: TranscriptionListResponse = response as any;
      console.log('加载历史记录成功:', data);

      // 将后端历史记录转换为会话格式并添加到会话列表
      if (data.data.items && data.data.items.length > 0) {
        const historyConversations: Conversation[] = data.data.items.map(item => ({
          key: `history_${item.id}`,
          label: item.title,
          timestamp: new Date(item.created_at).getTime(),
          messages: [],
          id: item.id,
          type: item.type,
          taskId: item.task_id, // 添加taskId字段映射
          taskStatus: item.task_status,
          summary_md: item.summary_md || undefined,
          overview_md: item.overview_md || undefined
        }));

        // 更新会话列表，保留本地的会话和已存在的历史记录
        setConversations(prevConversations => {
          // 分离本地会话（没有id或者isLocal为true的会话）和手动加载的会话
          const localConversations = prevConversations.filter(conv => 
            !conv.id || conv.isLocal === true || conv.isManuallyLoaded === true
          );
          
          // 获取现有历史记录的ID集合，避免重复添加
          const existingHistoryIds = new Set(
            prevConversations
              .filter(conv => conv.id && conv.isLocal !== true && conv.isManuallyLoaded !== true)
              .map(conv => conv.id)
          );
          
          // 创建现有会话的ID到会话的映射，用于保留状态
          const existingConversationsMap = new Map(
            prevConversations
              .filter(conv => conv.id && conv.isLocal !== true && conv.isManuallyLoaded !== true)
              .map(conv => [conv.id!, conv])
          );
          
          // 过滤掉已存在的历史记录，只添加新的
          const newHistoryConversations = historyConversations.filter(conv => 
            !existingHistoryIds.has(conv.id!)
          );
          
          // 更新现有的历史记录，保留本地状态（如taskStatus）
          const updatedExistingConversations = historyConversations
            .filter(conv => existingHistoryIds.has(conv.id!))
            .map(serverConv => {
              const existingConv = existingConversationsMap.get(serverConv.id!);
              if (existingConv) {
                // 保留本地的taskStatus，除非服务器返回了更新的状态
                const taskStatus = serverConv.taskStatus || existingConv.taskStatus;
                
                return {
                  ...existingConv, // 保留现有会话的所有状态
                  ...serverConv, // 使用服务器返回的最新数据
                  taskStatus: taskStatus, // 优先使用服务器状态，否则保留本地状态
                  messages: existingConv.messages || serverConv.messages // 保留已加载的消息
                };
              }
              return serverConv;
            });
          
          // 处理手动加载的会话与API返回的会话的合并
          const manuallyLoadedConversations = localConversations.filter(conv => conv.isManuallyLoaded === true);
          const pureLocalConversations = localConversations.filter(conv => conv.isManuallyLoaded !== true);
          
          // 检查手动加载的会话是否在API返回的历史记录中
          const mergedManualConversations = manuallyLoadedConversations.map(manualConv => {
            const apiConv = historyConversations.find(h => h.id === manualConv.id);
            if (apiConv) {
              // 合并手动加载的会话和API返回的数据
              return {
                ...apiConv,
                ...manualConv,
                // 保留手动加载标记
                isManuallyLoaded: true,
                // 保留已加载的消息
                messages: manualConv.messages.length > 0 ? manualConv.messages : apiConv.messages
              };
            }
            return manualConv;
          });
          
          // 过滤掉已经在手动加载会话中的历史记录
          const manuallyLoadedIds = new Set(manuallyLoadedConversations.map(conv => conv.id).filter(id => id !== undefined));
          const filteredNewHistoryConversations = newHistoryConversations.filter(conv => 
            !manuallyLoadedIds.has(conv.id!)
          );
          const filteredUpdatedConversations = updatedExistingConversations.filter(conv => 
            !manuallyLoadedIds.has(conv.id!)
          );
          
          console.log('会话列表更新:', {
            本地会话数量: pureLocalConversations.length,
            手动加载会话数量: manuallyLoadedConversations.length,
            现有历史记录数量: existingHistoryIds.size,
            更新的现有会话数量: filteredUpdatedConversations.length,
            新历史记录数量: filteredNewHistoryConversations.length,
            总数量: pureLocalConversations.length + mergedManualConversations.length + filteredUpdatedConversations.length + filteredNewHistoryConversations.length
          });
          
          return [...pureLocalConversations, ...mergedManualConversations, ...filteredUpdatedConversations, ...filteredNewHistoryConversations];
        });
      }
    } catch (error) {
      console.error('加载历史记录失败:', error);
      message.error('加载历史记录失败');
    }
  };

  // 修改历史记录加载函数，确保滚动到底部功能正常工作
  const loadTranscriptionDetail = async (id: number) => {
    try {
      console.log('开始加载记录详情, ID:', id);
      
      const response = await authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.getTranscription}/${id}`, {
        method: 'GET',
      });

      if (!response.success) {
        console.error('加载记录详情失败:', response.message);
        throw new Error(`服务器返回错误: ${response.code}, ${response.message}`);
      }

      const data: TranscriptionDetailResponse = response as any;
      console.log('加载记录详情成功:', data);

      // 将后端内容转换为消息格式
      if (data.data.content && data.data.content.length > 0) {
        const messages: TranscriptMessage[] = data.data.content.map((item, index) => {
          // 使用与实时录音一致的ID生成策略
          const messageId = `history_${id}_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
          
          console.log('创建历史记录消息:', {
            index,
            messageId,
            fullText: item.text,
            speaker: (item as any).speaker,
            start: item.start,
            end: item.end
          });
          
          return {
            id: messageId,
          content: item.text,
          isUser: false,
          timestamp: item.end || Date.now(),
            speaker: (item as any).speaker !== undefined ? (item as any).speaker : 0,
          start_time: (item as any).start_time || item.start,
          end_time: (item as any).end_time || item.end
          };
        });

        console.log('历史记录转换为消息格式:', {
          历史记录ID: id,
          原始内容数量: data.data.content.length,
          转换后消息数量: messages.length,
          消息ID示例: messages[0]?.id,
          消息内容示例: messages[0]?.content?.substring(0, 50),
          所有消息ID: messages.map(m => m.id)
        });

        // 更新会话内容，包括overview_md和summary_md
        setConversations(prevConversations => {
          console.log('loadTranscriptionDetail更新会话:', {
            targetId: id,
            conversationsCount: prevConversations.length,
            overviewMd: (data.data as any).overview_md?.length || 0,
            summaryMd: (data.data as any).summary_md?.length || 0
          });
          
          const updatedConversations = prevConversations.map(conv => {
            if (conv.id === id) {
              console.log('找到匹配会话，更新内容:', {
                conversationKey: conv.key,
                conversationLabel: conv.label,
                原有消息数: conv.messages.length,
                新消息数: messages.length,
                hasOverviewMd: !!(data.data as any).overview_md,
                overviewMdLength: (data.data as any).overview_md?.length || 0
              });
              return {
                ...conv,
                messages,
                // 保存summary_md到summary字段
                summary: (data.data as any).summary_md || conv.summary,
                // 保存overview_md到新字段
                overview_md: (data.data as any).overview_md
              };
            }
            return conv;
          });
          
          console.log('历史记录会话更新完成:', {
            targetId: id,
            更新前会话数: prevConversations.length,
            更新后会话数: updatedConversations.length,
            目标会话消息数: updatedConversations.find(c => c.id === id)?.messages.length || 0
          });
          
          return updatedConversations;
        });
      } else {
        console.warn('历史记录内容为空:', {
          历史记录ID: id,
          content: data.data.content
        });
      }

        // 在更新状态前先更新全局变量，确保其他组件能访问到最新状态
        window.__AUTO_SCROLL_ENABLED = false;
        // 更新React状态
        setShouldAutoScroll(false);

        // 添加延时处理，确保内容已经渲染
        setTimeout(() => {
          const transcriptContainers = document.querySelectorAll('.transcript-container');
          if (transcriptContainers.length > 0) {
            // 遍历所有容器
            transcriptContainers.forEach(container => {
              // 先滚动到顶部，查看完整内容
              (container as HTMLElement).scrollTop = 0;
              
              // 标记转写详情容器需要更新显示
              container.classList.add('force-visible');
              
              // 强制重绘
              window.requestAnimationFrame(() => {
                window.requestAnimationFrame(() => {
                  container.classList.remove('force-visible');
                  
                  // 确保为新加载的容器添加滚动监听器
                  container.removeEventListener('scroll', handleTranscriptScroll);
                  container.addEventListener('scroll', handleTranscriptScroll, { passive: true });
                });
              });
            });
          }
        }, 300);
    } catch (error) {
      console.error('加载记录详情失败:', error);
      message.error('加载记录详情失败');
    }
  };

  // 初始化加载历史记录
  useEffect(() => {
    // 只在activeTab变化时加载历史记录，不依赖conversations变化
    console.log('activeTab变化，加载历史记录:', activeTab);
    
    // 加载相应类型的历史记录
    if (activeTab === 'recording') {
      loadTranscriptionHistory('audio');
    } else if (activeTab === 'upload') {
      loadTranscriptionHistory('file');
    } else if (activeTab === 'summary') {
      // 总结页面需要显示所有转写历史记录（音频+文件），用于选择生成总结
      // 先加载音频记录，再加载文件记录
      loadTranscriptionHistory('audio').then(() => {
        loadTranscriptionHistory('file');
      });
    } else if (activeTab === 'translate') {
      // 翻译页面不需要显示转写历史记录，只保留本地会话（正在进行的录音/转写）
      setConversations(prevConversations => 
        prevConversations.filter(conv => !conv.id || conv.isLocal === true)
      );
    }
  }, [activeTab]); // 只依赖activeTab，移除conversations依赖
  
  // 处理闪烁问题：当没有选中任何会话时，显示默认的上传或录音界面
  useEffect(() => {
    // 清空文件列表，避免显示上一个会话的文件
    if (!activeConversation) {
      setFileList([]);
    }
  }, [activeConversation]);
  
  // 添加Modal状态管理
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    type: 'tab' | 'conversation';
    value: string;
    operationText: string;
  } | null>(null);

  // 修改处理Tab切换的函数
  const handleTabChange = (tab: string) => {
    console.log('切换标签页:', tab);
    
    // 检查是否有正在进行的操作
    const hasOngoingOperation = isRecording || isProcessing || generatingSummary;
    
    // 检查是否正在进行文件转写（isProcessing为true且不是录音状态）
    const isFileProcessing = isProcessing && !isRecording;
    
    // 检查是否是从上传文件转写切换到录音实时转写或生成总结
    const isFromUploadToRecordingOrSummary = (activeTab === 'upload' && (tab === 'recording' || tab === 'summary'));
    
    // 优先级1：如果正在进行文件转写，切换到任何标签页都不需要提示
    if (isFileProcessing) {
      console.log('正在文件转写时切换标签页，直接切换不显示确认提示');
      performTabChange(tab);
      return;
    }
    
    // 优先级2：如果是从上传文件转写切换到录音实时转写或生成总结，直接切换不显示确认提示
    if (isFromUploadToRecordingOrSummary) {
      console.log('从上传文件转写切换到录音实时转写或生成总结，直接切换不显示确认提示');
      performTabChange(tab);
      return;
    }
    
    // 优先级3：特殊处理：如果正在生成总结且用户点击的是总结Tab，直接切换
    if (generatingSummary && tab === 'summary') {
      console.log('正在生成总结时切换到总结tab，直接切换不显示确认提示');
      performTabChange(tab);
      return;
    }
    
    // 优先级4：检查其他正在进行的操作
    if (hasOngoingOperation) {
      let operationText = '';
      if (isRecording) {
        operationText = '录音转写';
      } else if (isProcessing) {
        operationText = '文件转写';
      } else if (generatingSummary) {
        operationText = '总结生成';
      }
      
      // 设置待执行的操作并显示确认对话框
      setPendingAction({
        type: 'tab',
        value: tab,
        operationText
      });
      setConfirmModalOpen(true);
    } else {
      // 没有正在进行的操作，直接切换
      performTabChange(tab);
    }
  };

  // Modal确认处理
  const handleModalOk = () => {
    if (pendingAction) {
      if (pendingAction.type === 'tab') {
        performTabChange(pendingAction.value);
      } else if (pendingAction.type === 'conversation') {
        performConversationChange(pendingAction.value);
      }
    }
    setConfirmModalOpen(false);
    setPendingAction(null);
  };

  // Modal取消处理
  const handleModalCancel = () => {
    console.log('用户取消切换操作');
    setConfirmModalOpen(false);
    setPendingAction(null);
  };

  // 测试网页提示弹窗处理函数
  const handleTestWarningOk = () => {
    // 保存同意状态到本地存储
    localStorage.setItem('testWarningAgreed', 'true');
    setTestWarningModalOpen(false);
    setPageAccessible(true);
  };
  
  // 处理轮播点击事件，弹出温馨提示框
  const handleMarqueeClick = () => {
    setTestWarningModalOpen(true);
  };
  
  // 实际执行标签切换的函数
  const performTabChange = (tab: string) => {
    // 检查是否为有效的标签类型
    const validTabs = ['recording', 'upload', 'summary', 'translate'];
    if (!validTabs.includes(tab)) {
      return;
    }
    
    const tabType = tab as TabType;
    
    // 如果正在录音，先停止录音并保存记录
    if (isRecording && transcriber.current) {
      console.log('切换标签页时停止录音并立即保存记录');
      
      // 使用公共函数立即保存当前录音记录
      saveCurrentRecordingImmediately('切换标签页').then((success) => {
        if (!success) {
          console.warn('切换标签页时保存录音记录失败');
        }
      });
      
      // 然后停止录音
      handleStopRecording();
    }
    
    // 点击tab时的默认行为
    // 如果当前已选中某个会话且切换的Tab与该会话类型一致，则不清除选择
    const currentConv = conversations.find(conv => conv.key === activeConversation);
    
    if (currentConv) {
      const isFileType = currentConv.fileInfo || (currentConv.type === 'file');
      const isRecordingType = currentConv.recordingTime || (currentConv.type === 'audio');
      
      // 如果切换到的Tab与当前会话类型不一致，则清除选择
      if ((tabType === 'upload' && !isFileType) || 
          (tabType === 'recording' && !isRecordingType)) {
        setActiveConversation('');
        setFileList([]);
      }
    } else {
      // 如果没有选中的会话，清空选择
      setActiveConversation('');
      setFileList([]);
    }
    
    // 设置当前活动Tab
    setActiveTab(tabType);
    
    // 清空不相关的历史记录
    if (tabType === 'upload' || tabType === 'recording') {
      // 过滤会话列表，只保留与当前tab类型相关的会话
      setConversations(prevConversations => {
        // 保留本地会话（没有id的会话）
        const localConversations = prevConversations.filter(conv => !conv.id);
        
        // 根据tab类型过滤历史记录
        const filteredHistoryConversations = prevConversations.filter(conv => {
          if (!conv.id) return false; // 跳过本地会话
          
          if (tabType === 'upload') {
            return conv.fileInfo || conv.type === 'file';
          } else if (tabType === 'recording') {
            return conv.recordingTime || conv.type === 'audio';
          }
          return false;
        });
        
        // 如果过滤后的历史记录为空，则只返回本地会话
        if (filteredHistoryConversations.length === 0) {
          return localConversations;
        }
        
        // 合并本地会话和过滤后的历史记录
        return [...localConversations, ...filteredHistoryConversations];
      });
      
      // 加载相应类型的历史记录
      // 检查是否有正在处理的任务，如果有则不重新加载历史记录
      const hasProcessingTasks = conversations.some(conv => 
        conv.taskStatus && 
        conv.taskStatus !== 'done' && 
        conv.taskStatus !== 'failed' && 
        conv.taskStatus !== 'convert_failed'
      );
      
      if (!hasProcessingTasks) {
        if (tabType === 'recording') {
          loadTranscriptionHistory('audio');
        } else if (tabType === 'upload') {
          loadTranscriptionHistory('file');
        }
      } else {
        console.log('检测到正在处理的任务，跳过Tab切换时的历史记录重新加载');
      }
    }
    
    // 如果切换到summary, translate等非转写界面，隐藏按钮并禁用自动滚动
    if (tabType !== 'recording' && tabType !== 'upload') {
      // 更新全局变量
      window.__AUTO_SCROLL_ENABLED = false;
      // 更新React状态
      setShouldAutoScroll(false);
    }
  };
  
  // 修改会话切换函数，处理历史记录
  const handleConversationChange = (key: string) => {
    
    // 检查是否是历史记录项目（以history_开头）
    const isHistoryItem = key.startsWith('history_');
    
    // 检查当前活动会话是否也是历史记录项目
    const currentIsHistoryItem = activeConversation.startsWith('history_');
    
    // 检查是否有正在进行的操作
    const hasOngoingOperation = isRecording || isProcessing || generatingSummary;
    
    // 如果是历史记录项目，直接切换，不显示确认提示
    if (isHistoryItem) {
      performConversationChange(key);
      return;
    }
    
    // 如果当前是历史记录项目且切换到另一个会话，也不显示确认提示
    if (currentIsHistoryItem) {
      performConversationChange(key);
      return;
    }
    
    // 检查是否正在进行文件转写（isProcessing为true表示正在处理文件）
    const isFileProcessing = isProcessing && !isRecording;
    
    // 如果正在进行文件转写，切换到任何会话都不需要提示
    if (isFileProcessing) {
      performConversationChange(key);
      return;
    }
    
    if (hasOngoingOperation) {
      let operationText = '';
      if (isRecording) {
        operationText = '录音转写';
      } else if (isProcessing) {
        operationText = '文件转写';
      } else if (generatingSummary) {
        operationText = '总结生成';
      }
      
      // 设置待执行的操作并显示确认对话框
      setPendingAction({
        type: 'conversation',
        value: key,
        operationText
      });
      setConfirmModalOpen(true);
    } else {
      // 没有正在进行的操作，直接切换
      performConversationChange(key);
    }
  };
  
  // 实际执行会话切换的函数
  const performConversationChange = (key: string) => {
    // 如果正在录音，先停止录音并保存记录
    if (isRecording && transcriber.current) {
      console.log('切换会话时停止录音并立即保存记录');
      
      // 使用公共函数立即保存当前录音记录
      saveCurrentRecordingImmediately('切换会话').then((success) => {
        if (!success) {
          console.warn('切换会话时保存录音记录失败');
        }
      });
      
      // 然后停止录音
      handleStopRecording();
    }
    
    setActiveConversation(key);
    
    // 如果是历史记录且没有消息内容，加载详情
    const currentConv = conversations.find(conv => conv.key === key);
    if (currentConv && currentConv.id && (!currentConv.messages || currentConv.messages.length === 0)) {
      loadTranscriptionDetail(currentConv.id);
    }
    
    // 检查任务状态，如果不是完成或失败状态，则启动轮询
    if (currentConv && currentConv.taskId && currentConv.taskStatus) {
      const status = currentConv.taskStatus;
      
      // 如果任务状态不是完成或失败状态，则继续轮询
      if (status !== 'done' && status !== 'failed' && status !== 'convert_failed') {
        
        // 启动轮询监控任务进度
        if (currentConv.id) {
          pollTranscriptionProgress(currentConv.id, key);
        }
      }
    }
    
    // 如果是历史记录，需要设置fileInfo
    if (currentConv && currentConv.id && !currentConv.fileInfo && !currentConv.recordingTime) {
      // 根据key前缀判断是否是历史记录
      if (key.startsWith('history_')) {
        // 判断数据类型
        const isFileType = currentConv.key.includes('file') || conversations.find(c => c.id === currentConv.id)?.type === 'file';
        
        if (isFileType) {
          // 更新会话，添加fileInfo
          setConversations(prevConversations => {
            return prevConversations.map(conv => {
              if (conv.key === key) {
                return {
                  ...conv,
                  fileInfo: {
                    name: conv.label,
                    uid: `history_${conv.id}`,
                  }
                };
              }
              return conv;
            });
          });
        } else {
          // 音频类型，添加recordingTime
          setConversations(prevConversations => {
            return prevConversations.map(conv => {
              if (conv.key === key) {
                return {
                  ...conv,
                  recordingTime: '00:00'
                };
              }
              return conv;
            });
          });
        }
      }
    }
    
    // 更新全局变量
    window.__LATEST_ACTIVE_CONVERSATION = key;
    
    // 根据会话类型切换到对应的Tab
    if (currentConv) {
      if (currentConv.fileInfo || (currentConv.id && key.startsWith('history_') && currentConv.type === 'file')) {
        setActiveTab('upload');
      } else if (currentConv.recordingTime || (currentConv.id && key.startsWith('history_') && currentConv.type === 'audio')) {
        setActiveTab('recording');
      }
      
      // 移除自动跳转到总结页面的逻辑，让用户手动控制
      // 不再因为有总结就自动跳转，保持在当前适合的tab
      
      // 如果切换会话时当前在总结页面，且新会话没有总结，则跳转到合适的页面
      if (activeTab === 'summary' && !currentConv.summary && !currentConv.summary_md) {
        if (currentConv.fileInfo || (currentConv.id && key.startsWith('history_') && currentConv.type === 'file')) {
          setActiveTab('upload');
        } else if (currentConv.recordingTime || (currentConv.id && key.startsWith('history_') && currentConv.type === 'audio')) {
          setActiveTab('recording');
        }
      }
    }
  };
  
  const handleStartRecording = async () => {
    if (!transcriber.current) return;
    
    // 自动创建新会话
    const newKey = await handleNewConversation();
    
    // 启用自动滚动
    window.__AUTO_SCROLL_ENABLED = true;
    setShouldAutoScroll(true);
    
    try {
      // 确保全局变量同步更新
      window.__LATEST_ACTIVE_CONVERSATION = newKey;
      
      setIsProcessing(true);
      setIsRecording(true);
      await transcriber.current.startRecording();
      
      // 切换到录音Tab
      setActiveTab('recording');
    } catch (error) {
      console.error('开始录音失败:', error);
      setIsRecording(false);
      setIsProcessing(false);
      message.error('录音失败，请检查麦克风权限');
    }
  };
  
  const handleStopRecording = () => {
    if (!transcriber.current) return;
    
    // 显示等待蒙层
    setIsWaitingForResult(true);
    setWaitingProgress(0);
    setWaitingMessage('正在等待服务器返回最终结果...');
    
    // 启动进度条动画
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += 100 / 200; // 20秒内完成，每100ms增加0.5%
      setWaitingProgress(Math.min(progress, 95)); // 最多到95%，留5%等待实际完成
    }, 100);
    
    // 设置20秒超时
    const maxWaitTimeout = setTimeout(() => {
      console.log('等待20秒超时，强制结束等待');
      clearInterval(progressInterval);
      setWaitingProgress(100);
      setWaitingMessage('处理完成');
      
      // 延迟500ms后隐藏蒙层
      setTimeout(() => {
        setIsWaitingForResult(false);
        setWaitingProgress(0);
      }, 500);
    }, 20000);
    
    // 添加录音完成回调，保存录音记录
    if (transcriber.current) {
      // 保存当前的activeConversation，避免闭包问题
      const currentConvKey = activeConversation;
      
      // 设置WebSocket结果回调
      transcriber.current.setOnWebSocketResult(() => {
        console.log('收到WebSocket最终结果，提前结束等待');
        clearTimeout(maxWaitTimeout);
        clearInterval(progressInterval);
        setWaitingProgress(100);
        setWaitingMessage('处理完成');
        
        // 延迟500ms后隐藏蒙层
        setTimeout(() => {
          setIsWaitingForResult(false);
          setWaitingProgress(0);
        }, 500);
      });
      
      transcriber.current.setOnProcessingComplete(() => {
        // 使用ref获取最新的conversations
        const latestConversations = conversationsRef.current;
        if (currentConvKey && latestConversations.length > 0) {
          const currentConv = latestConversations.find(conv => conv.key === currentConvKey);
          if (currentConv && currentConv.messages && currentConv.messages.length > 0) {
            // 检查会话是否已经保存过
            if (savedConversationKeysRef.current.has(currentConvKey)) {
              console.log('会话已保存过，跳过重复保存');
              return;
            }
            
            // 使用公共函数保存录音记录
            saveCurrentRecordingImmediately('正常停止录音').then((success) => {
              if (!success) {
                console.warn('正常停止录音时保存录音记录失败');
              }
            });
          } else {
            console.warn('未找到录音会话或会话无内容:', currentConvKey);
          }
        }
      });
    }
    
    transcriber.current.stopRecording();
    setIsRecording(false);
    
    // 保存录音时间到当前会话
    if (activeConversation) {
      setConversations(prevConversations => {
        return prevConversations.map(conv => {
          if (conv.key === activeConversation) {
            return {
              ...conv,
              recordingTime: recordTime,
              // 明确标记会话类型为audio
              type: 'audio'
              // 注意：不要在这里设置isLocal: true，因为会话可能已经保存过了
            };
          }
          return conv;
        });
      });
    }
  };
  
  // 统一的错误处理函数
  const handleUploadError = (error: any, currentSessionKey: string, errorType?: string) => {
    console.error('文件上传处理失败:', error);
    console.log('handleUploadError被调用，参数:', {
      error: error?.message || error,
      currentSessionKey,
      errorType
    });
    
    // 清理已创建的会话状态
    setConversations(prevConversations => 
      prevConversations.filter(conv => conv.key !== currentSessionKey)
    );
    setActiveConversation('');
    setFileList([]);
    setIsProcessing(false);
    
    // 根据错误类型提供不同的提示信息
    let errorMessage = '文件上传失败，请重新选择文件上传';
    let userFriendlyMessage = '上传失败，请重新选择文件';
    
    if (errorType === 'parse_error') {
      errorMessage = '服务器响应格式异常，可能是网络问题，请稍后重试';
      userFriendlyMessage = '响应异常，请稍后重试';
    } else if (errorType === 'task_id_missing') {
      errorMessage = '服务器未返回有效的任务ID，请重新选择文件上传';
      userFriendlyMessage = '上传异常，请重新选择文件';
    } else if (errorType === 'create_record_failed') {
      errorMessage = '创建转写记录失败，可能是服务器繁忙，请稍后重试';
      userFriendlyMessage = '创建记录失败，请稍后重试';
    } else if (errorType === 'transcription_id_missing') {
      errorMessage = '服务器未返回转写记录ID，请稍后重试';
      userFriendlyMessage = '服务器响应异常，请稍后重试';
    } else if (error.message === 'upload_timeout' || errorType === 'upload_timeout') {
      errorMessage = '上传超时，请检查网络或文件大小';
      userFriendlyMessage = '上传超时，请刷新页面重试';
    } else if (error.message.includes('文件上传失败')) {
      errorMessage = '网络连接异常或服务器繁忙，请稍后重试';
      userFriendlyMessage = '网络异常，请稍后重试';
    } else if (error.message.includes('不支持的文件格式')) {
      errorMessage = error.message;
      userFriendlyMessage = '文件格式不支持';
    } else if (error.message.includes('文件大小')) {
      errorMessage = error.message;
      userFriendlyMessage = '文件过大';
    } else if (error.message.includes('服务器响应格式异常')) {
      errorMessage = '服务器响应格式异常，可能是网络问题，请稍后重试';
      userFriendlyMessage = '响应异常，请稍后重试';
    } else if (error.message.includes('文件头验证失败')) {
      errorMessage = error.message;
      userFriendlyMessage = '文件格式验证失败';
    } else if (error.message.includes('可能是伪装攻击') || error.message.includes('格式验证失败')) {
      errorMessage = error.message;
      userFriendlyMessage = '检测到文件格式伪装攻击';
    } else if (error.message.includes('文件名')) {
      errorMessage = error.message;
      userFriendlyMessage = '文件名不符合要求';
    } else if (error.message.includes('检测到可疑') || error.message.includes('扩展名')) {
      errorMessage = error.message;
      userFriendlyMessage = '检测到不安全的文件';
    } else if (error.message.includes('文件验证失败') || error.message.includes('无效的文件对象')) {
      errorMessage = error.message;
      userFriendlyMessage = '文件验证失败';
    } else if (errorType === 'file_header_validation_failed') {
      errorMessage = error.message;
      userFriendlyMessage = '文件格式验证失败，请确保上传的是有效的音频文件';
    } else if (errorType === 'format_spoofing_detected') {
      errorMessage = error.message;
      userFriendlyMessage = '检测到文件格式伪装攻击，请上传真实的音频文件';
    } else if (errorType === 'filename_validation_failed') {
      errorMessage = error.message;
      userFriendlyMessage = '文件名不符合要求，请重新命名后上传';
    } else if (errorType === 'suspicious_file_detected') {
      errorMessage = error.message;
      userFriendlyMessage = '检测到不安全的文件，请选择其他文件';
    } else if (errorType === 'file_validation_failed') {
      errorMessage = error.message;
      userFriendlyMessage = '文件验证失败，请检查文件是否完整';
    }
    
    console.log('设置处理状态和错误消息:', {
      userFriendlyMessage,
      errorMessage,
      errorType
    });
    
    setProcessingStatus({ 
      message: userFriendlyMessage, 
      type: 'error' 
    });
    
    // 显示错误消息
    console.log('即将显示错误消息:', {
      主标题: '文件处理失败',
      详细信息: errorMessage,
      持续时间: 6
    });
    
    // 针对不同错误类型显示特殊的消息提示
    if (errorType === 'upload_timeout') {
      messageApi.error({
        content: `⏰ ${errorMessage || '文件上传超时，请检查网络或文件大小'}`,
        duration: 8,
        style: {
          marginTop: '20vh',
          fontSize: '16px',
          fontWeight: 'bold',
          zIndex: 10000
        }
      });
    } else if (errorType === 'file_header_validation_failed') {
      messageApi.error({
        content: `📄 ${errorMessage || '文件头验证失败，这不是有效的音频文件'}`,
        duration: 8,
        style: {
          marginTop: '20vh',
          fontSize: '16px',
          fontWeight: 'bold',
          zIndex: 10000
        }
      });
    } else if (errorType === 'format_spoofing_detected') {
      messageApi.error({
        content: `🛡️ ${errorMessage || '检测到文件格式伪装攻击，文件的实际格式与扩展名不匹配'}`,
        duration: 8,
        style: {
          marginTop: '20vh',
          fontSize: '16px',
          fontWeight: 'bold',
          zIndex: 10000
        }
      });
    } else if (errorType === 'suspicious_file_detected') {
      messageApi.error({
        content: `🔒 ${errorMessage || '检测到不安全的文件，不允许上传可能包含恶意代码的文件'}`,
        duration: 8,
        style: {
          marginTop: '20vh',
          fontSize: '16px',
          fontWeight: 'bold',
          zIndex: 10000
        }
      });
    } else if (errorType === 'video_validation_failed' || error.message?.includes('不支持视频文件')) {
      messageApi.error({
        content: `${error.message || '不支持视频文件，请上传纯音频格式的MP4文件'}`,
        duration: 8,
        style: {
          marginTop: '20vh',
          fontSize: '16px',
          fontWeight: 'bold',
          zIndex: 10000
        }
      });
    } else {
      messageApi.error({
        content: `⚠️ ${errorMessage || '文件处理失败'}`,
        duration: 8,
        style: {
          marginTop: '20vh',
          fontSize: '16px',
          fontWeight: 'bold',
          zIndex: 10000
        }
      });
    }
    
    console.log('错误消息已调用message.error，设置5秒后清除状态');
    
    // 5秒后清除状态，让用户可以重新开始
    setTimeout(() => {
      setProcessingStatus(null);
      console.log('处理状态已清除');
    }, 5000);
  };

  const handleFileUpload: UploadProps['onChange'] = async (info) => {
    const { file } = info;
    console.log('文件上传事件:', file.status, file);
    
    // 如果是验证失败的文件，跳过处理
    if (file && file.status === 'error') {
      console.log('文件验证失败，跳过处理');
      return;
    }
    
    if (file && file.status === 'done') {
      // 检查是否为实际的文件对象（不是undefined/null）
      const actualFile = file.originFileObj || file;
      if (!actualFile || typeof actualFile.name !== 'string') {
        console.log('文件对象无效，跳过处理');
        return;
      }
      
      // 能到达这里的文件都是通过了beforeUpload验证的
      console.log('文件上传完成，开始处理:', actualFile.name);
      
      console.log('检测到有效文件, 开始处理');
      // 设置文件列表以显示文件
      setFileList([file]);
      
      // 启用自动滚动
      window.__AUTO_SCROLL_ENABLED = true;
      setShouldAutoScroll(true);
      
      // 自动创建新会话，使用文件名作为会话名称
      let currentSessionKey = '';
      console.log('上传文件时自动创建新会话');
      // 获取文件名，去掉扩展名
      let fileName = '';
      if (actualFile.name) {
        fileName = actualFile.name.split('.').slice(0, -1).join('.');
        if (!fileName) {
          fileName = actualFile.name; // 如果无法提取名称（可能只有扩展名），则使用完整文件名
        }
      }
      currentSessionKey = await handleNewConversation(fileName || undefined);
      
      // 保存文件信息到会话
      setConversations(prevConversations => {
        return prevConversations.map(conv => {
          if (conv.key === currentSessionKey) {
            return {
              ...conv,
              fileInfo: {
                name: actualFile.name,
                uid: file.uid || Date.now().toString(),
              },
              // 明确标记会话类型为file
              type: 'file',
              isLocal: true
            };
          }
          return conv;
        });
      });
      
      try {
        console.log('开始异步上传音频文件:', actualFile.name, '使用会话:', currentSessionKey);
        setIsProcessing(true);
        
        // 切换到上传Tab
        setActiveTab('upload');
        
        // 步骤1: 上传文件到新的接口获取task_id
        console.log('步骤1: 上传文件到服务器');
        setProcessingStatus({ message: '正在上传文件...', type: 'processing' });
        
        const token = localStorage.getItem('dy-token');
        if (!token) {
          throw new Error('未找到登录凭证');
        }
        const app_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyX2UzOTlmZTVmIiwiZXhwIjoxNzU0MDE3NTkwLjAzMDIzNH0.TcRFH8jBhLqD73HwNydLl7hbfuhGCwM3eWVqf8h99v8"
        
        // 创建FormData上传文件
        const formData = new FormData();
        // 使用实际的文件对象
        const fileObj = actualFile;
        if (!fileObj) {
          throw new Error('无法获取文件对象');
        }
        
        // 安全的文件验证 - 确保使用真实的File对象
        const realFile = (fileObj as any).originFileObj || fileObj;
        if (!(realFile instanceof File)) {
          throw new Error('无效的文件对象');
        }
        const secureValidation = await validateFileSecurely(realFile);
        if (!secureValidation.isValid) {
          throw new Error(secureValidation.error || '文件验证失败');
        }
        
        // 检测文件类型并设置Content-Type
        const supportedMimeTypes = [
          "audio/aac",
          "audio/wav", 
          "video/mp4",
          "audio/mp4",
          "audio/x-ms-wma",
          "audio/ogg",
          "video/ogg",
          "audio/amr",
          "audio/flac"
        ];
        
        // 获取文件的MIME类型
        let contentType = fileObj.type;
        
        // 安全地根据文件扩展名推断MIME类型
        if (!contentType || !supportedMimeTypes.includes(contentType)) {
          const fileName = fileObj.name.toLowerCase();
          // 获取最后一个扩展名（避免双扩展名问题）
          const parts = fileName.split('.');
          const extension = parts[parts.length - 1];
          
          switch (extension) {
            case 'aac':
              contentType = 'audio/aac';
              break;
            case 'wav':
              contentType = 'audio/wav';
              break;
            case 'mp4':
              contentType = 'video/mp4';
              break;
            case 'm4a':
              contentType = 'audio/mp4';
              break;
            case 'wma':
              contentType = 'audio/x-ms-wma';
              break;
            case 'ogg':
              contentType = 'audio/ogg';
              break;
            case 'amr':
              contentType = 'audio/amr';
              break;
            case 'flac':
              contentType = 'audio/flac';
              break;
            case 'mp3':
              contentType = 'audio/mpeg';
              break;
            default:
              // 如果无法识别，使用默认值
              contentType = 'audio/mp4';
          }
        }
        
        // 验证是否为支持的类型
        if (!supportedMimeTypes.includes(contentType) && contentType !== 'audio/mpeg') {
          throw new Error(`不支持的文件类型: ${contentType}。支持的格式包括: ${supportedMimeTypes.join(', ')}`);
        }
        
        console.log('检测到文件类型:', contentType, '文件名:', fileObj.name);
        
        // 创建带有正确Content-Type的File对象
        const fileWithCorrectType = new File([realFile], realFile.name, {
          type: contentType,
          lastModified: realFile.lastModified
        });
        
        // 添加文件到FormData
        formData.append('payload', fileWithCorrectType);
        
        // 上传文件到新的接口，设置超时处理
        const uploadResponse = await Promise.race([
          fetch('https://api.dianyaai.com/api/transcribe/upload', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${app_token}`
            },
            body: formData
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('upload_timeout')), 120000) // 2分钟超时
          )
        ]) as Response;
        
        if (!uploadResponse.ok) {
          const errorText = await uploadResponse.text();
          console.error('上传请求失败:', uploadResponse.status, errorText);
          throw new Error(`文件上传失败: ${uploadResponse.status}, ${errorText}`);
        }
        
        let uploadData;
        try {
          uploadData = await uploadResponse.json();
          console.log('文件上传响应数据:', uploadData);
        } catch (parseError) {
          console.error('解析上传响应失败:', parseError);
          handleUploadError(parseError, currentSessionKey, 'parse_error');
          return; // 直接返回，不继续执行
        }
        
        // 更健壮的task_id验证
        const taskId = uploadData?.task_id;
        console.log('task_id详细验证信息:', {
          uploadData,
          taskId: taskId,
          taskIdType: typeof taskId,
          taskIdTruthy: !!taskId,
          taskIdIsString: typeof taskId === 'string',
          taskIdTrimmed: typeof taskId === 'string' ? taskId.trim() : 'N/A',
          taskIdTrimmedEmpty: typeof taskId === 'string' ? taskId.trim() === '' : 'N/A',
          validationResult: !taskId || typeof taskId !== 'string' || taskId.trim() === ''
        });
        
        if (!taskId || typeof taskId !== 'string' || taskId.trim() === '') {
          console.error('上传响应异常，未获取到有效的task_id:', {
            uploadData,
            taskId: taskId,
            taskIdType: typeof taskId,
            taskIdEmpty: !taskId || (typeof taskId === 'string' && taskId.trim() === ''),
            originalTaskId: uploadData?.task_id,
            reason: !taskId ? 'task_id不存在' : 
                    typeof taskId !== 'string' ? 'task_id不是字符串类型' :
                    'task_id是空字符串'
          });
          
          console.log('即将调用handleUploadError，参数:', {
            error: 'task_id缺失或无效',
            currentSessionKey,
            errorType: 'task_id_missing'
          });
          
          handleUploadError(
            new Error('task_id缺失或无效'), 
            currentSessionKey, 
            'task_id_missing'
          );
          return; // 直接返回，不抛出异常
        }
        
        console.log('task_id获取成功:', taskId);
        
        // 步骤2: 创建转写记录
        console.log('步骤2: 创建转写记录，task_id:', taskId);
        setProcessingStatus({ message: '正在创建转写记录...', type: 'processing' });
        
        const createResponse = await authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.createTranscription}`, {
          method: 'POST',
          body: JSON.stringify({
            title: fileName || file.name,
            type: 'file',
            content: [],
            task_id: taskId
          })
        });
        
        if (!createResponse.success) {
          console.error('创建转写记录失败:', createResponse);
          // 对于创建转写记录失败，使用特殊处理
          handleUploadError(
            new Error(`创建转写记录失败: ${createResponse.code}, ${createResponse.message}`), 
            currentSessionKey, 
            'create_record_failed'
          );
          return;
        }
        
        const createData = createResponse;
        console.log('转写记录创建成功:', createData);
        
        const transcriptionId = createData.data?.id;
        if (!transcriptionId) {
          console.error('创建转写记录响应中缺少ID:', createData);
          handleUploadError(
            new Error('创建转写记录响应中缺少ID'), 
            currentSessionKey, 
            'transcription_id_missing'
          );
          return;
        }
        
        // 更新会话信息，保存服务器ID
        setConversations(prevConversations => {
          return prevConversations.map(conv => {
            if (conv.key === currentSessionKey) {
              console.log('handleFileUpload更新会话状态:', {
                key: conv.key,
                label: conv.label,
                oldTaskStatus: conv.taskStatus,
                newTaskStatus: 'processing',
                taskId: taskId,
                id: transcriptionId
              });
              return {
                ...conv,
                serverId: transcriptionId,
                id: transcriptionId,
                taskId: taskId,
                taskStatus: 'processing', // 添加初始状态
                isLocal: false // 立即设置为false，这样就能在历史记录中显示
              };
            }
            return conv;
          });
        });
        
        // 步骤3: 开始轮询转写进度
        console.log('步骤3: 开始轮询转写进度，转写ID:', transcriptionId);
        await pollTranscriptionProgress(transcriptionId, currentSessionKey);
        
      } catch (error: any) {
        // 检查错误类型并设置对应的errorType
        let errorType: string | undefined;
        
        if (error.message === 'upload_timeout') {
          errorType = 'upload_timeout';
        } else if (error.message.includes('不支持视频文件')) {
          errorType = 'video_validation_failed';
        } else if (error.message.includes('文件头验证失败')) {
          errorType = 'file_header_validation_failed';
        } else if (error.message.includes('可能是伪装攻击') || error.message.includes('格式验证失败')) {
          errorType = 'format_spoofing_detected';
        } else if (error.message.includes('文件名') && (error.message.includes('无效') || error.message.includes('非法') || error.message.includes('过长'))) {
          errorType = 'filename_validation_failed';
        } else if (error.message.includes('检测到可疑') || error.message.includes('扩展名')) {
          errorType = 'suspicious_file_detected';
        } else if (error.message.includes('文件验证失败') || error.message.includes('无效的文件对象')) {
          errorType = 'file_validation_failed';
        }
        
        handleUploadError(error, currentSessionKey, errorType);
      }
    }
  };
  
  const handleNewConversation = async (label?: string) => {
    // 如果正在录音，先停止录音
    if (isRecording && transcriber.current) {
      transcriber.current.stopRecording();
      setIsRecording(false);
    }
    
    // 启用自动滚动
    window.__AUTO_SCROLL_ENABLED = true;
    setShouldAutoScroll(true);
    
    const timestamp = Date.now();
    const newKey = timestamp.toString();
    const newConversation: Conversation = {
      key: newKey,
      label: label || formatDateTime(timestamp),
      timestamp: timestamp,
      messages: [],
      // 添加一个临时标记，表示这是本地会话，还未保存到服务器
      isLocal: true
    };
    
    // 将新会话添加到列表最前面，但不显示在历史记录中
    setConversations(prevConversations => [newConversation, ...prevConversations]);
    
    // 确保活动会话ID已经更新
    setActiveConversation(newKey);
    window.__LATEST_ACTIVE_CONVERSATION = newKey;
    console.log('新建会话并设置为活动:', newKey);
    
    return newKey;
  };
  
  const handleGenerateSummary = async (templateType: string = 'report', recordId?: number) => {
    if (!transcriber.current) return;
    
    try {
      setGeneratingSummary(true);
      // 保存总结生成状态到localStorage
      saveSummaryGeneratingState(true, recordId);

      // 只有在确实需要生成总结时才切换到总结页面
      // 不要在加载历史记录详情时自动跳转
      if (!recordId || !conversations.find(conv => conv.id === recordId && conv.summary)) {
        // 立即切换到总结页面，让用户看到生成进度
        setActiveTab('summary');
      }
      
      // 禁用自动滚动
      window.__AUTO_SCROLL_ENABLED = false;
      setShouldAutoScroll(false);
      
      // 三种情况：
      // 1. 从总结面板选择了转写记录并传入了recordId
      // 2. 从录音转写或文件转写页面点击"生成总结"按钮（已有currentConversation）
      // 3. 没有选择任何记录
      
      // 移除重复的事件触发，SideMenu会在activeTab变化时自动加载
      // document.dispatchEvent(new CustomEvent('loadSummaryList'));
      
      // 如果传入了recordId，优先使用该ID
      if (recordId) {
        // 查找对应的会话信息，以获取标题
        const conversation = conversations.find(conv => conv.id === recordId);
        const title = conversation ? conversation.label : '转写记录总结';
        
        console.log('使用传入的recordId生成总结:', recordId, '标题:', title);
        
        // 创建目标会话key
        const targetConversationKey = `history_${recordId}`;
        
        // 立即设置当前活动会话
        setActiveConversation(targetConversationKey);
        window.__LATEST_ACTIVE_CONVERSATION = targetConversationKey;
        console.log('设置活动会话为:', targetConversationKey);
        
        // 异步生成总结
        transcriber.current.generateSummaryById(
          recordId,
          `${title}的总结`
        ).then(summary => {
          console.log('总结生成成功，开始更新状态:', { recordId, summaryLength: summary?.length });
          
          // 使用函数式更新，同时处理会话创建和总结更新
          setConversations(prevConversations => {
            // 查找是否已存在对应会话
            const existingConversationIndex = prevConversations.findIndex(conv => conv.key === targetConversationKey);
            
            if (existingConversationIndex >= 0) {
              // 如果会话已存在，更新总结
              console.log('找到已存在会话，更新总结:', prevConversations[existingConversationIndex].label);
              const updatedConversations = [...prevConversations];
              updatedConversations[existingConversationIndex] = {
                ...updatedConversations[existingConversationIndex],
                summary
              };
              return updatedConversations;
            } else {
              // 如果会话不存在，创建新会话并包含总结
              console.log('创建新会话并包含总结:', targetConversationKey);
              return [
                {
                  key: targetConversationKey,
                  label: title,
                  timestamp: Date.now(),
                  messages: [],
                  id: recordId,
                  summary
                },
                ...prevConversations
              ];
            }
          });
          
          // 总结生成成功后，重新加载转写详情以获取完整信息（包括overview_md）
          console.log('总结生成成功，重新加载转写详情以获取overview_md');
          console.log('准备重新加载转写详情，recordId:', recordId);
          loadTranscriptionDetail(recordId);
          
          // 生成总结成功后，刷新总结列表
          document.dispatchEvent(new CustomEvent('refreshSummaryList'));
          
          // 触发总结生成成功事件，传递转写记录ID
          document.dispatchEvent(new CustomEvent('summaryGenerated', {
            detail: { transcriptionId: recordId }
          }));
          
          console.log('总结生成完成，触发相关事件');
          message.success('总结生成成功');
        }).catch(error => {
          console.error('生成总结失败:', error);
          message.error('生成总结失败，请稍后再试');
        }).finally(() => {
          setGeneratingSummary(false);
          // 清除保存的总结生成状态
          saveSummaryGeneratingState(false);
        });
      }
      // 检查当前是否有选中的会话且有ID
      else if (currentConversation && currentConversation.id) {
        console.log('使用当前选中会话生成总结, ID:', currentConversation.id);
        
        // 异步生成总结
        transcriber.current.generateSummaryById(
          currentConversation.id,
          `${currentConversation.label}的总结`
        ).then(summary => {
          // 更新会话总结
          setConversations(prevConversations => {
            return prevConversations.map(conv => {
              if (conv.key === activeConversation) {
                return {
                  ...conv,
                  summary
                };
              }
              return conv;
            });
          });
          
          // 总结生成成功后，重新加载转写详情以获取完整信息（包括overview_md）
          console.log('总结生成成功，重新加载转写详情以获取overview_md');
          console.log('准备重新加载转写详情，currentConversation:', {
            key: currentConversation.key,
            id: currentConversation.id,
            hasId: !!currentConversation.id,
            idType: typeof currentConversation.id
          });
          if (currentConversation.id) {
            loadTranscriptionDetail(currentConversation.id);
          } else {
            console.error('currentConversation.id不存在，无法重新加载转写详情');
          }
          
          // 生成总结成功后，刷新总结列表
          document.dispatchEvent(new CustomEvent('refreshSummaryList'));
          
          // 触发总结生成成功事件，传递转写记录ID
          document.dispatchEvent(new CustomEvent('summaryGenerated', {
            detail: { transcriptionId: currentConversation.id }
          }));
          
          message.success('总结生成成功');
        }).catch((error: any) => {
          console.error('生成总结失败:', error);
          message.error('生成总结失败，请稍后再试');
        }).finally(() => {
          setGeneratingSummary(false);
          // 清除保存的总结生成状态
          saveSummaryGeneratingState(false);
        });
      } else {
        // 如果当前没有选中的会话或会话没有ID，则提示用户
        console.log('没有选择有效的转写记录');
        message.error('请先选择一个已保存到服务器的转写记录');
        setGeneratingSummary(false);
        // 清除保存的总结生成状态
        saveSummaryGeneratingState(false);
      }
    } catch (error) {
      console.error('生成总结失败:', error);
      message.error('生成总结失败，请稍后再试');
      setGeneratingSummary(false);
      // 清除保存的总结生成状态
      saveSummaryGeneratingState(false);
    }
  };
  
  // 获取当前选中的会话
  const currentConversation = useMemo(() => {
    const conversation = conversations.find(conv => conv.key === activeConversation);
    console.log('currentConversation计算:', {
      activeConversation,
      conversationsCount: conversations.length,
      foundConversation: !!conversation,
      conversationSummary: conversation?.summary?.length || 0
    });
    return conversation;
  }, [conversations, activeConversation]);
  
  const hasTranscriptData = useMemo(() => 
    Boolean(currentConversation?.messages.length && currentConversation.messages.length > 0),
  [currentConversation]);
  
  const hasSummary = useMemo(() => 
    Boolean(currentConversation?.summary || currentConversation?.summary_md),
  [currentConversation]);
  
  const hasOverview = useMemo(() => {
    // 修改为与hasSummary一致的判断条件
    const result = Boolean(currentConversation?.summary || currentConversation?.summary_md);
    return result;
  }, [currentConversation, activeConversation]);
  
  const handleExportPDF = async (content: string, exportType: 'summary' | 'overview' = 'summary') => {
    if (!content) {
      messageApi.error('没有可导出的内容');
      return;
    }
    
    let loadingMessage: any = null;
    
    try {
      // 初始化PDF
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      
      // 检查字体状态
      let fontSetupSuccess = setupFontsForPDF(doc);
      
      // 如果字体设置失败，等待字体加载完成
      if (!fontSetupSuccess) {
        loadingMessage = messageApi.open({
          type: 'loading',
          content: '正在等待字体加载完成，请稍候...',
          duration: 0,
        });
        console.log('字体未就绪，等待加载完成...');
        
        const fontLoadSuccess = await waitForFontsToLoad();
        
        if (loadingMessage) {
          messageApi.destroy();
          loadingMessage = null;
        }
        
        if (fontLoadSuccess) {
          // 重新尝试设置字体
          fontSetupSuccess = setupFontsForPDF(doc);
          console.log('字体加载完成，重新设置字体结果:', fontSetupSuccess);
        } else {
          console.warn('字体加载失败或超时，将使用默认字体');
          messageApi.warning('字体加载失败，将使用默认字体导出PDF');
        }
      }
      
      // 开始生成PDF
      loadingMessage = messageApi.open({
        type: 'loading',
        content: '正在生成PDF...',
        duration: 0,
      });
      
      // 设置全局行高变量和宽度变量
      const lineHeight = 6; // 行高，同时也用作段落间距
      const paragraphSpacing = lineHeight * 0.6; // 段落间距，设为行高的0.6倍
      const maxWidth = 190; // 页面内容宽度
      const maxY = 270; // 最大高度
      const footerMargin = 15; // 页脚上方的空白边距
      
      // 添加标题
      doc.setFontSize(18);
      doc.text('转写内容总结', 105, 20, { align: 'center' });
      
      // 处理Markdown内容
      const lines = content.split('\n');
      let y = 40;
      
      // 设置左右边距，使其对称
      const leftMargin = 20;
      const rightMargin = 20;
      const contentWidth = 210 - leftMargin - rightMargin; // A4纸宽度210mm减去左右边距
      
      // 添加特殊标题列表，用于识别需要额外间距的标题
      const specialHeadings = ['待办事项', '详细总结'];
      
      // 预处理连续的空行，将多个连续空行压缩为一个
      const processedLines = [];
      let emptyLineCount = 0;
      
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim() === '') {
          emptyLineCount++;
          // 只有当这是第一个空行时才添加
          if (emptyLineCount === 1) {
            processedLines.push('');
          }
          // 连续的空行忽略
        } else {
          // 非空行，重置计数器并添加
          emptyLineCount = 0;
          processedLines.push(lines[i]);
        }
      }
      
      // 使用处理后的行数组
      for (let i = 0; i < processedLines.length; i++) {
        let line = processedLines[i];
        
        // 检查当前行处理后是否会超出页面
        // 预估当前行的高度
        let estimatedHeight = lineHeight;
        if (line.trim().startsWith('#')) {
          estimatedHeight = lineHeight + 3; // 标题需要更多空间
        } else if (line.trim().length > 0) {
          // 估算可能的换行数量
          const potentialLineCount = Math.ceil(line.length / (maxWidth / 12)); // 将字符宽度从6增加到12，减少换行
          estimatedHeight = lineHeight * potentialLineCount;
        }
        
        // 如果当前行加上估计高度会超出页面(考虑页脚边距)，先添加新页
        if (y + estimatedHeight + footerMargin > maxY) {
          doc.addPage();
          y = 20; // 新页面从顶部开始
        }
        
        // 处理加粗语法 **文本**
        line = line.replace(/\*\*(.*?)\*\*/g, '$1');
        
        // 处理标题
        const headingMatch = line.match(/^(#{1,6})\s+(.*?)$/);
        if (headingMatch) {
          const headingLevel = headingMatch[1].length;
          const headingText = headingMatch[2];
          
          // 检查是否是特殊标题，如"详细总结"或"待办事项"
          const isSpecialHeading = specialHeadings.some(heading => headingText.includes(heading));
          
          // 根据标题级别设置不同的段前段后间距
          switch (headingLevel) {
            case 1: // h1: 没有段前，段后为1.5倍行高
              // 不增加段前间距
              // 根据标题级别设置字体大小
              doc.setFontSize(18);
              if (fontSetupSuccess) {
                doc.setFont('msyh', 'bold'); // 设置标题加粗
              }
              doc.text(headingText, leftMargin, y);
              if (fontSetupSuccess) {
                doc.setFont('msyh', 'normal'); // 恢复正常字体
              }
              doc.setFontSize(12);
              y += lineHeight * 1.5; // 段后为1.5倍行高
              break;
            
            case 2: // h2: 段前为0.75倍行高，段后为1倍行高
              y += lineHeight * 0.75; // 段前为0.75倍行高
              doc.setFontSize(16);
              if (fontSetupSuccess) {
                doc.setFont('msyh', 'bold'); // 设置标题加粗
              }
              doc.text(headingText, leftMargin, y);
              if (fontSetupSuccess) {
                doc.setFont('msyh', 'normal'); // 恢复正常字体
              }
              doc.setFontSize(12);
              y += lineHeight; // 添加段后间距，确保与列表之间有足够空间
              break;
            
            case 3: // h3: 段前段后各0.75倍行高
              y += lineHeight * 0.4; // 段前间距，从0.75减小到0.5
              
              // 详细总结标题前增加额外间距
              if (headingText.includes('详细总结')) {
                y += lineHeight * 0.8; // 额外增加前间距，从0.5增加到0.8
              }
              
              doc.setFontSize(14);
              if (fontSetupSuccess) {
                doc.setFont('msyh', 'bold'); // 设置标题加粗
              }
              doc.text(headingText, leftMargin, y);
              if (fontSetupSuccess) {
                doc.setFont('msyh', 'normal'); // 恢复正常字体
              }
              doc.setFontSize(12);
              
              // 根据标题内容调整段后间距
              if (headingText.includes('详细总结')) {
                y += lineHeight * 0.1; // 详细总结标题后间距进一步减小，从0.2减小到0.1
              } else if (headingText.includes('待办事项')) {
                y += lineHeight * 0.75; // 段后间距
                y += lineHeight * 0.5; // 额外间距
              } else {
                y += lineHeight * 0.75; // 段后间距
              }
              break;
            
            default: // h4-h6: 段前段后各0.5倍行高
              y += lineHeight * 0.5; // 段前间距
              doc.setFontSize(20 - headingLevel * 2);
              if (fontSetupSuccess) {
                doc.setFont('msyh', 'bold'); // 设置标题加粗
              }
              doc.text(headingText, leftMargin, y);
              if (fontSetupSuccess) {
                doc.setFont('msyh', 'normal'); // 恢复正常字体
              }
              doc.setFontSize(12);
              y += lineHeight * 0.6; // 段后间距，从0.5增加到0.6
              break;
          }
          
          // 检查下一行是否是列表项或空行，如果不是，则添加额外间距
          if (i < processedLines.length - 1) {
            const nextLine = processedLines[i+1];
            const isNextLineList = nextLine.match(/^(\s*)(\d+)\.\s+(.*?)$/) || nextLine.match(/^(\s*)[-*+]\s+(.*?)$/);
            const isNextLineEmpty = nextLine.trim() === '';
            
            // 如果是特殊标题（如"待办事项"），且下一行不是列表项也不是空行，添加额外间距
            if (isSpecialHeading && !isNextLineList && !isNextLineEmpty) {
              // 对于详细总结标题，减少额外间距
              if (headingText.includes('详细总结')) {
                y += lineHeight * 0.1; // 进一步减少额外间距，从0.2减小到0.1
              } else {
                y += lineHeight; // 对其他特殊标题保持原有间距
              }
            }
            // 对于待办事项标题，如果下一行是列表项，添加额外间距
            else if (headingText.includes('待办事项') && isNextLineList) {
              y += lineHeight * 0.5; // 待办事项标题后面紧跟列表时，添加额外间距
            }
            // 对于普通标题，如果下一行是列表项，添加额外间距
            else if (!isSpecialHeading && isNextLineList) {
              y += lineHeight * 0.5; // 标题后面紧跟列表时，添加额外间距
            }
          }
          
          continue;
        }
        
        // 处理无序列表
        const unorderedListMatch = line.match(/^(\s*)[-*+]\s+(.*?)$/);
        if (unorderedListMatch) {
          const indent = unorderedListMatch[1].length;
          const listText = unorderedListMatch[2];
          
          // 检查是否是该级别的第一个无序列表项
          const prevLine = i > 0 ? processedLines[i-1] : '';
          const isPrevLineList = prevLine.match(/^(\s*)[-*+]\s+(.*?)$/);
          const isPrevLineSameIndent = isPrevLineList && isPrevLineList[1].length === indent;
          const isPrevLineHeading = prevLine.match(/^(#{1,6})\s+(.*?)$/);
          
          // 如果是第一个列表项，添加适当间距
          if (!isPrevLineSameIndent && !isPrevLineHeading) {
            y += lineHeight * 0.5; // 如果前一行不是标题，则添加间距
          }
          
          const bulletX = leftMargin + (indent * 5);
          doc.text('•', bulletX, y);
          
          const textLines = doc.splitTextToSize(listText, contentWidth - (bulletX - leftMargin) - 5);
          doc.text(textLines, bulletX + 5, y);
          
          // 更新y位置，确保考虑多行文本
          y += lineHeight * textLines.length;
          continue;
        }
        
        // 处理有序列表
        const orderedListMatch = line.match(/^(\s*)(\d+)\.\s+(.*?)$/);
        if (orderedListMatch) {
          const indent = orderedListMatch[1].length;
          const number = orderedListMatch[2];
          const listText = orderedListMatch[3];
          
          // 检查前一行是否是标题或不同类型的列表
          const prevLine = i > 0 ? processedLines[i-1] : '';
          const isPrevLineList = prevLine.match(/^(\s*)(\d+)\.\s+(.*?)$/);
          const isPrevLineSameIndent = isPrevLineList && isPrevLineList[1].length === indent;
          const isPrevLineHeading = prevLine.match(/^(#{1,6})\s+(.*?)$/);
          
          // 如果前一行不是相同缩进的列表项且不是标题，则添加间距
          if (!isPrevLineSameIndent && !isPrevLineHeading) {
            y += lineHeight * 0.5;
          }
          
          // 调整数字位置，使其靠后一些
          const numberX = leftMargin + 5 + (indent * 5);
          doc.text(`${number}.`, numberX, y);
          
          // 减小数字和内容之间的间距（从10减小到5）
          const textLines = doc.splitTextToSize(listText, contentWidth - (numberX - leftMargin) - 5);
          doc.text(textLines, numberX + 5, y);
          
          // 更新y位置，确保考虑多行文本
          y += lineHeight * textLines.length;
          continue;
        }
        
        // 处理引用块
        const blockquoteMatch = line.match(/^>\s+(.*?)$/);
        if (blockquoteMatch) {
          const quoteText = blockquoteMatch[1];
          
          doc.setDrawColor(200, 200, 200);
          doc.line(leftMargin, y - 3, leftMargin, y + 3);
          
          const textLines = doc.splitTextToSize(quoteText, contentWidth - 10);
          doc.text(textLines, leftMargin + 10, y);
          
          // 更新y位置，确保考虑多行文本
          y += lineHeight * textLines.length;
          continue;
        }
        
        // 处理普通文本段落
        if (line.trim() === '') {
          // 空行
          y += lineHeight; // 空行使用与正常行相同的间距
        } else {
          // 处理文本，确保标点符号不会单独出现在下一行
          // 使用更直接的方法处理孤标点问题
          const processedLine = line;
          
          // 使用jsPDF的widthOfString方法计算字符宽度
          // 然后手动控制换行，确保标点符号不会单独成行
          const words = processedLine.split('');
          let currentLine = '';
          const resultLines = [];
          
          for (let j = 0; j < words.length; j++) {
            const currentChar = words[j];
            const nextChar = words[j + 1] || '';
            
            // 检查当前字符加上下一个字符是否会超出宽度
            const testLine = currentLine + currentChar;
            const testWidth = doc.getStringUnitWidth(testLine) * doc.getFontSize() * 0.6; // 乘以0.6减小计算宽度，防止过早换行
            
            // 如果是标点符号，尝试与前一个字符放在同一行
            const isPunctuation = /[，。！？；：、,.!?;:]/.test(currentChar);
            
            if (testWidth > maxWidth) {
              // 如果当前字符是标点符号且是该行唯一字符，将其添加到上一行
              if (isPunctuation && currentLine === '') {
                if (resultLines.length > 0) {
                  resultLines[resultLines.length - 1] += currentChar;
                } else {
                  resultLines.push(currentChar);
                }
                currentLine = '';
              } else {
                // 正常换行
                resultLines.push(currentLine);
                currentLine = currentChar;
              }
            } else {
              // 检查下一个字符是否是标点符号，如果是且加上后会超出宽度，则提前换行
              const nextIsPunctuation = /[，。！？；：、,.!?;:]/.test(nextChar);
              const testLineWithNext = testLine + nextChar;
              const testWidthWithNext = doc.getStringUnitWidth(testLineWithNext) * doc.getFontSize() * 0.6; // 乘以0.6减小计算宽度，防止过早换行
              
              if (nextIsPunctuation && testWidthWithNext > maxWidth) {
                // 如果下一个是标点符号且加上后会超出宽度，当前行先结束
                resultLines.push(testLine);
                currentLine = '';
                j++; // 跳过下一个字符(标点符号)，因为已经处理了
              } else {
                // 正常添加当前字符
                currentLine = testLine;
              }
            }
          }
          
          // 添加最后一行
          if (currentLine !== '') {
            resultLines.push(currentLine);
          }
          
          // 对于每一行文本，单独检查是否需要分页
          for (let lineIndex = 0; lineIndex < resultLines.length; lineIndex++) {
            // 检查当前行是否会超出页面底部(考虑页脚边距)
            if (y + lineHeight + footerMargin > maxY) {
              doc.addPage();
              y = 20; // 新页面从顶部开始
            }
            
            // 渲染当前行文本
            doc.text([resultLines[lineIndex]], leftMargin, y);
            
            // 更新y位置
            y += lineHeight;
          }
          
          // 段落间距与行间距相同
          if (i < processedLines.length - 1 && processedLines[i+1].trim() !== '') {
            // 检查添加段落间距后是否会超出页面底部
            if (y + paragraphSpacing + footerMargin > maxY) {
              doc.addPage();
              y = 20; // 新页面从顶部开始
            } else {
              y += lineHeight * 0.8; // 增加段落间距，从原来的paragraphSpacing(lineHeight * 0.6)增加到lineHeight * 0.8
            }
          }
        }
      }
      
      // 添加页脚
      const pageCount = doc.getNumberOfPages();
      doc.setFontSize(10);
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.text(`生成时间: ${formatDateTime(Date.now())}`, leftMargin, 285);
        doc.text(`${i} / ${pageCount}`, 210 - rightMargin, 285, { align: 'right' });
      }
      
      // 保存PDF
      const fileName = `转写总结_${formatDateTime(Date.now()).replace(/[: ]/g, '_')}.pdf`;
      doc.save(fileName);
      
      if (loadingMessage) {
        messageApi.destroy();
      }
      
      const messageText = exportType === 'overview' ? '速览已导出为PDF' : '总结已导出为PDF';
      messageApi.success(fontSetupSuccess ? messageText : messageText);
    } catch (error) {
      console.error('PDF导出失败:', error);
      if (loadingMessage) {
        messageApi.destroy();
      }
      messageApi.error('PDF导出失败，请重试');
    }
  };
  
  const handleExportTranscriptPDF = async () => {
    if (!currentConversation || !currentConversation.messages.length) {
      messageApi.error('没有可导出的转写内容');
      return;
    }
    
    let loadingMessage: any = null;
    
    try {
      // 初始化PDF
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      
      // 检查字体状态
      let fontSetupSuccess = setupFontsForPDF(doc);
      
      // 如果字体设置失败，等待字体加载完成
      if (!fontSetupSuccess) {
        loadingMessage = messageApi.open({
          type: 'loading',
          content: '正在等待字体加载完成，请稍候...',
          duration: 0,
        });
        console.log('字体未就绪，等待加载完成...');
        
        const fontLoadSuccess = await waitForFontsToLoad();
        
        if (loadingMessage) {
          messageApi.destroy();
          loadingMessage = null;
        }
        
        if (fontLoadSuccess) {
          // 重新尝试设置字体
          fontSetupSuccess = setupFontsForPDF(doc);
          console.log('字体加载完成，重新设置字体结果:', fontSetupSuccess);
        } else {
          console.warn('字体加载失败或超时，将使用默认字体');
          messageApi.warning('字体加载失败，将使用默认字体导出PDF');
        }
      }
      
      // 开始生成PDF
      loadingMessage = messageApi.open({
        type: 'loading',
        content: '正在生成PDF...',
        duration: 0,
      });
      
      // 设置全局行高变量和宽度变量
      const lineHeight = 6; // 行高，同时也用作段落间距
      const paragraphSpacing = lineHeight * 0.6; // 段落间距，设为行高的0.6倍
      const maxWidth = 190; // 页面内容宽度
      const maxY = 270; // 最大高度
      const footerMargin = 15; // 页脚上方的空白边距
      
      // 添加标题
      doc.setFontSize(18);
      doc.text(`${currentConversation.label} - 转写`, 105, 20, { align: 'center' });
      doc.setFontSize(12);

      // 合并所有转写文本
      const transcriptText = currentConversation.messages
        .filter((message, index, self) => index === self.findIndex(m => m.id === message.id))
        .map(message => message.content)
        .join('\n\n');
      
      // 处理文本内容
      const lines = transcriptText.split('\n');
      let y = 40;
      
      // 设置左右边距，使其对称
      const leftMargin = 20;
      const rightMargin = 20;
      const contentWidth = 210 - leftMargin - rightMargin; // A4纸宽度210mm减去左右边距
      
      for (let i = 0; i < lines.length; i++) {
        let line = lines[i];
        
        // 处理加粗语法 **文本**
        line = line.replace(/\*\*(.*?)\*\*/g, '$1');
        
        // 如果是空行，添加适当的间距
        if (line.trim() === '') {
          y += lineHeight; // 空行使用与正常行相同的间距
          continue;
        }
        
        // 检查是否需要分页
        if (y + lineHeight + footerMargin > maxY) {
          doc.addPage();
          y = 20; // 新页面从顶部开始
        }
        
        // 处理文本，确保标点符号不会单独出现在下一行
        const words = line.split('');
        let currentLine = '';
        const resultLines = [];
        
        for (let j = 0; j < words.length; j++) {
          const currentChar = words[j];
          const nextChar = words[j + 1] || '';
          
          // 检查当前字符加上下一个字符是否会超出宽度
          const testLine = currentLine + currentChar;
          const testWidth = doc.getStringUnitWidth(testLine) * doc.getFontSize() * 0.6;
          
          // 如果是标点符号，尝试与前一个字符放在同一行
          const isPunctuation = /[，。！？；：、,.!?;:]/.test(currentChar);
          
          if (testWidth > maxWidth) {
            // 如果当前字符是标点符号且是该行唯一字符，将其添加到上一行
            if (isPunctuation && currentLine === '') {
              if (resultLines.length > 0) {
                resultLines[resultLines.length - 1] += currentChar;
              } else {
                resultLines.push(currentChar);
              }
              currentLine = '';
            } else {
              // 正常换行
              resultLines.push(currentLine);
              currentLine = currentChar;
            }
          } else {
            // 检查下一个字符是否是标点符号，如果是且加上后会超出宽度，则提前换行
            const nextIsPunctuation = /[，。！？；：、,.!?;:]/.test(nextChar);
            const testLineWithNext = testLine + nextChar;
            const testWidthWithNext = doc.getStringUnitWidth(testLineWithNext) * doc.getFontSize() * 0.6;
            
            if (nextIsPunctuation && testWidthWithNext > maxWidth) {
              // 如果下一个是标点符号且加上后会超出宽度，当前行先结束
              resultLines.push(testLine);
              currentLine = '';
              j++; // 跳过下一个字符(标点符号)，因为已经处理了
            } else {
              // 正常添加当前字符
              currentLine = testLine;
            }
          }
        }
        
        // 添加最后一行
        if (currentLine !== '') {
          resultLines.push(currentLine);
        }
        
        // 对于每一行文本，单独检查是否需要分页
        for (let lineIndex = 0; lineIndex < resultLines.length; lineIndex++) {
          // 检查当前行是否会超出页面底部
          if (y + lineHeight + footerMargin > maxY) {
            doc.addPage();
            y = 20; // 新页面从顶部开始
          }
          
          // 渲染当前行文本
          doc.text([resultLines[lineIndex]], leftMargin, y);
          
          // 更新y位置
          y += lineHeight;
        }
        
        // 段落间距
        if (i < lines.length - 1 && lines[i+1].trim() !== '') {
          // 检查添加段落间距后是否会超出页面底部
          if (y + paragraphSpacing + footerMargin > maxY) {
            doc.addPage();
            y = 20; // 新页面从顶部开始
          } else {
            y += paragraphSpacing;
          }
        }
      }
      
      // 添加页脚
      const pageCount = doc.getNumberOfPages();
      doc.setFontSize(10);
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.text(`生成时间: ${formatDateTime(Date.now())}`, leftMargin, 285);
        doc.text(`${i} / ${pageCount}`, 210 - rightMargin, 285, { align: 'right' });
      }
      
      // 保存PDF
      const fileName = `${currentConversation.label}_转写_${formatDateTime(Date.now()).replace(/[: ]/g, '_')}.pdf`;
      doc.save(fileName);
      
      if (loadingMessage) {
        messageApi.destroy();
      }
      
      messageApi.success(fontSetupSuccess ? '转写内容已导出为PDF' : '转写内容已导出为PDF');
    } catch (error) {
      console.error('转写PDF导出失败:', error);
      if (loadingMessage) {
        messageApi.destroy();
      }
      messageApi.error('转写PDF导出失败，请重试');
    }
  };
  
  // 添加新函数：加载历史记录但保留当前详情
  const loadHistoryAndKeepDetail = async (type: 'file' | 'audio', currentConvId?: number) => {
    try {
      console.log('开始加载历史记录并保留详情, 类型:', type, '当前会话ID:', currentConvId);
      
      const response = await authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.listTranscriptions}?type=${type}`, {
        method: 'GET',
      });

      if (!response.success) {
        console.error('加载历史记录失败:', response.message);
        throw new Error(`服务器返回错误: ${response.code}, ${response.message}`);
      }

      const data: TranscriptionListResponse = response as any;
      console.log('加载历史记录成功:', data);

      // 将后端历史记录转换为会话格式并添加到会话列表，但保留当前会话的详情
      if (data.data.items && data.data.items.length > 0) {
        const historyConversations: Conversation[] = data.data.items.map(item => ({
          key: `history_${item.id}`,
          label: item.title,
          timestamp: new Date(item.created_at).getTime(),
          messages: [], // 默认为空消息
          id: item.id,
          type: item.type,
          taskId: item.task_id, // 添加taskId字段映射
          taskStatus: item.task_status,
          summary_md: item.summary_md || undefined,
          overview_md: item.overview_md || undefined
        }));

        // 更新会话列表，保留本地的会话和当前会话的详情
        setConversations(prevConversations => {
          // 过滤出本地会话（没有id的会话）
          const localConversations = prevConversations.filter(conv => !conv.id);
          
          // 合并历史会话，但保留当前会话的详情
          const mergedConversations = historyConversations.map(newConv => {
            // 查找是否有匹配的现有会话（具有相同id且有消息内容）
            const existingConv = prevConversations.find(
              conv => conv.id === newConv.id && conv.messages && conv.messages.length > 0
            );
            
            // 如果找到匹配的会话且该会话有消息内容，或者是当前选中的会话，则保留其消息内容
            if (existingConv && (existingConv.messages.length > 0 || existingConv.id === currentConvId)) {
              return {
                ...newConv,
                messages: existingConv.messages,
                summary: existingConv.summary,
                fileInfo: existingConv.fileInfo || (newConv.type === 'file' ? { name: newConv.label, uid: `history_${newConv.id}` } : undefined),
                recordingTime: existingConv.recordingTime || (newConv.type === 'audio' ? '00:00' : undefined)
              };
            }
            
            return newConv;
          });
          
          return [...localConversations, ...mergedConversations];
        });
      }
    } catch (error) {
      console.error('加载历史记录失败:', error);
      message.error('加载历史记录失败');
    }
  };

  // 修改saveTranscriptionRecord函数，支持更新模式
  const saveTranscriptionRecord = async (conversation: Conversation, type: 'file' | 'audio', serverId?: number) => {
    if (!conversation || !conversation.messages || conversation.messages.length === 0) {
      console.warn('没有可保存的转写内容');
      return;
    }
    
    try {
      console.log('开始保存转写记录, 类型:', type, '会话:', conversation.key, '消息数量:', conversation.messages.length, 'serverId:', serverId);
      
      // 准备请求数据
      const utterances = conversation.messages.map(msg => ({
        text: msg.content,
        start: msg.timestamp - 5000, // 估算开始时间
        end: msg.timestamp
      }));
      
      // 确保有内容可以发送
      if (utterances.length === 0) {
        console.warn('转写内容为空，跳过保存');
        return;
      }
      
      console.log('准备发送数据:', {
        title: conversation.label,
        type: type,
        contentCount: utterances.length,
        serverId: serverId,
        firstMessage: utterances[0]?.text.substring(0, 20) + '...',
        lastMessage: utterances[utterances.length - 1]?.text.substring(0, 20) + '...'
      });
      
      // 构造请求数据
      const requestData: any = {
        title: conversation.label,
        type: type,
        content: utterances
      };
      
      let apiUrl: string;
      let method: string;
      
      // 如果有serverId，则调用更新接口；否则调用创建接口
      if (serverId) {
        requestData.id = serverId;
        apiUrl = `${API_CONFIG.baseURL}${API_ENDPOINTS.updateTranscription}`;
        method = 'PUT';
        console.log('调用更新接口, serverId:', serverId);
      } else {
        apiUrl = `${API_CONFIG.baseURL}${API_ENDPOINTS.createTranscription}`;
        method = 'POST';
        console.log('调用创建接口');
      }
      
      console.log('请求URL:', apiUrl);
      console.log('请求方法:', method);
      console.log('发送请求体大小:', JSON.stringify(requestData).length, '字节');
      
      // 构造请求
      const response = await authenticatedApiRequest(apiUrl, {
        method: method,
        body: JSON.stringify(requestData)
      });
      
      console.log('请求完成, 状态码:', response.code);
      
      if (!response.success) {
        console.error('服务器返回错误内容:', response.message);
        throw new Error(`服务器返回错误: ${response.code}, ${response.message}`);
      }
      
      const responseData = response;
      console.log('转写记录已保存到服务器, 响应数据:', responseData);
      
      // 更新会话信息，保存服务器返回的ID
      if (responseData.data && responseData.data.id) {
        const newServerId = responseData.data.id;
        
        // 更新当前会话，添加serverId并移除isLocal标记
        setConversations(prevConversations => {
          return prevConversations.map(conv => {
            if (conv.key === conversation.key) {
              return {
                ...conv,
                serverId: newServerId, // 保存服务器ID用于后续更新
                id: newServerId, // 同时更新id字段
                isLocal: false // 移除本地标记，让会话显示在历史记录中
              };
            }
            return conv;
          });
        });
        
        // 如果是首次保存（没有传入serverId），显示保存成功消息
        if (!serverId) {
          message.success('转写记录已保存');
        } else {
          console.log('转写记录已更新, ID:', serverId);
        }
        
        return newServerId; // 返回服务器ID
      }
    } catch (error) {
      console.error('保存转写记录失败:', error);
      message.error('保存转写记录失败');
      throw error;
    }
  };
  
  // 返回到主页面
  const handleBackToMain = () => {
    // 如果正在录音或处理，先停止
    if (isRecording || isProcessing) {
      if (isRecording) {
        handleStopRecording();
      }
      
      if (transcriber.current) {
        // 安全地停止处理
        try {
          if (transcriber.current.isCurrentlyRecording()) {
            transcriber.current.stopRecording();
          }
        } catch (e) {
          console.error('停止录音失败:', e);
        }
      }
      
      setIsProcessing(false);
    }
    
    // 使用自定义事件返回主界面，不改变URL
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('backToMain');
      document.body.dispatchEvent(event);
    }
  };
  
  // 监听backToMain事件
  useEffect(() => {
    const handleBackEvent = () => {
      console.log('返回到主界面...');
    };
    
    document.body.addEventListener('backToMain', handleBackEvent);
    
    return () => {
      document.body.removeEventListener('backToMain', handleBackEvent);
    };
  }, []);
  
  // 删除转写记录函数
  const handleDeleteTranscription = async (id: number) => {
    try {
      const apiUrl = `${API_CONFIG.baseURL}${API_ENDPOINTS.deleteTranscription}/${id}`;
      
      // 发送删除请求
      const response = await authenticatedApiRequest(apiUrl, {
        method: 'DELETE',
      });
      
      if (!response.success) {
        throw new Error(`删除失败: ${response.message}`);
      }
      
      // 从会话列表中移除已删除的记录
      setConversations(prevConversations => 
        prevConversations.filter(conv => conv.id !== id)
      );
      
      // 如果当前选中的是被删除的记录，清除选择
      if (activeConversation && activeConversation.startsWith(`history_${id}`)) {
        setActiveConversation('');
        setFileList([]);
      }
      
      message.success('记录已成功删除');
    } catch (error) {
      console.error('删除转写记录失败:', error);
      message.error('删除转写记录失败，请重试');
    }
  };
  
  // 添加从总结跳转到转写记录的函数
  const handleJumpToTranscription = (transcriptionId: number) => {
    console.log('跳转到转写记录, ID:', transcriptionId);
    
    // 寻找对应的会话
    const conversation = conversations.find(conv => conv.id === transcriptionId);
    
    if (conversation) {
      // 找到了对应的会话，切换到它
      setActiveConversation(conversation.key);
      
      // 清除会话中的消息，确保重新加载详情
      setConversations(prevConversations => {
        return prevConversations.map(conv => {
          if (conv.id === transcriptionId) {
            return {
              ...conv,
              messages: [] // 清空消息，强制重新加载
            };
          }
          return conv;
        });
      });
      
      // 不管是否有详情，都重新加载详情，确保显示最新内容
      setTimeout(() => {
        loadTranscriptionDetail(transcriptionId);
      }, 50); // 短暂延时确保状态更新后再加载
      
      // 切换到对应的tab（录音或文件）
      if (conversation.type === 'audio') {
        setActiveTab('recording');
      } else if (conversation.type === 'file') {
        setActiveTab('upload');
      }
      
      // 显示成功消息
      message.success('已跳转到对应的转写记录');
      return true; // 返回true表示跳转成功
    } else {
      // 没有找到对应的会话，先尝试直接获取该记录的详情
      console.log('未找到转写记录，尝试直接获取记录详情...');
      
      const loadSpecificTranscriptionAndJump = async () => {
        try {
          // 先尝试直接获取指定ID的转写记录详情
          const response = await authenticatedApiRequest(
            `${API_CONFIG.baseURL}${API_ENDPOINTS.getTranscription}/${transcriptionId}`,
            { method: 'GET' }
          );

          if (response.success) {
            const data: TranscriptionDetailResponse = response as any;
            console.log('成功获取转写记录详情:', data);

            // 创建新的会话对象
            const newConversation: Conversation = {
              key: `history_${transcriptionId}`,
              label: data.data.title || `记录 ${transcriptionId}`,
              timestamp: new Date(data.data.created_at || Date.now()).getTime(),
              messages: [],
              id: transcriptionId,
              type: data.data.type as 'audio' | 'file',
              summary: (data.data as any).summary_md,
              overview_md: (data.data as any).overview_md,
              // 标记为手动加载的会话，避免被历史记录刷新时移除
              isManuallyLoaded: true
            };

            // 将新会话添加到会话列表的开头
            let updatedConversationsList: Conversation[] = [];
            setConversations(prevConversations => {
              // 检查是否已存在相同ID的会话，避免重复
              const existingIndex = prevConversations.findIndex(conv => conv.id === transcriptionId);
              if (existingIndex !== -1) {
                // 如果已存在，更新该会话
                updatedConversationsList = [...prevConversations];
                updatedConversationsList[existingIndex] = newConversation;
              } else {
                // 如果不存在，添加到开头
                updatedConversationsList = [newConversation, ...prevConversations];
              }
              // 立即更新 ref，确保后续操作能访问到新会话
              conversationsRef.current = updatedConversationsList;
              return updatedConversationsList;
            });

            // 设置为当前活动会话
            setActiveConversation(newConversation.key);

            // 加载会话详情（消息内容）
            setTimeout(() => {
              loadTranscriptionDetail(transcriptionId);
            }, 100);

            // 切换到对应的tab
            if (newConversation.type === 'audio') {
              setActiveTab('recording');
            } else if (newConversation.type === 'file') {
              setActiveTab('upload');
            }

            message.success('已跳转到对应的转写记录');
            return true;
          } else {
            throw new Error(response.message || '获取转写记录失败');
          }
        } catch (error) {
          console.error('获取转写记录详情失败:', error);
          
          // 如果直接获取失败，再尝试加载历史记录
          console.log('直接获取失败，尝试加载历史记录...');
          try {
            // 先加载音频历史
            await loadTranscriptionHistory('audio');
            // 再加载文件历史
            await loadTranscriptionHistory('file');
            
            // 加载完成后再次尝试查找会话
            const updatedConversation = conversationsRef.current.find(
              conv => conv.id === transcriptionId
            );
            
            if (updatedConversation) {
              // 找到了对应的会话，切换到它
              setActiveConversation(updatedConversation.key);
              
              // 重新加载详情
              loadTranscriptionDetail(transcriptionId);
              
              // 切换到对应的tab
              if (updatedConversation.type === 'audio') {
                setActiveTab('recording');
              } else if (updatedConversation.type === 'file') {
                setActiveTab('upload');
              }
              
              message.success('已跳转到对应的转写记录');
              return true;
            } else {
              message.error('未找到对应的转写记录');
              return false;
            }
          } catch (historyError) {
            console.error('加载历史记录失败:', historyError);
            message.error('加载转写记录失败');
            return false;
          }
        }
      };
      
      // 执行加载和查找流程
      return loadSpecificTranscriptionAndJump();
    }
  };
  
  // 确保在组件挂载时设置初始滚动状态
  useEffect(() => {
    // 默认启用自动滚动
    if (typeof window !== 'undefined') {
      window.__AUTO_SCROLL_ENABLED = true;
    }
    setShouldAutoScroll(true);
    console.log('组件挂载，初始化滚动状态');

    // 组件挂载时立即加载默认tab的历史记录
    if (activeTab === 'recording') {
      loadTranscriptionHistory('audio');
    } else if (activeTab === 'upload') {
      loadTranscriptionHistory('file');
    }

    // 检查是否有保存的总结生成状态需要恢复
    const savedState = getSavedSummaryGeneratingState();
    if (savedState && savedState.isGenerating) {
      console.log('检测到保存的总结生成状态，准备恢复:', savedState);
      // 切换到总结页面
      setActiveTab('summary');
      // 如果有recordId，尝试恢复生成状态
      if (savedState.recordId) {
        // 延迟一下，确保页面已经切换到总结页面
        setTimeout(() => {
          console.log('恢复总结生成状态，recordId:', savedState.recordId);
          // 这里可以选择是否重新开始生成，或者只是显示正在生成的状态
          // 为了安全起见，我们只恢复UI状态，不重新开始生成
          // 用户可以手动重新生成
        }, 100);
      }
    }
  }, []);

  const handleViewSummary = () => {
    console.log('handleViewSummary 被调用');
    console.log('currentConversation:', currentConversation);
    console.log('currentConversation?.summary:', currentConversation?.summary);
    console.log('currentConversation?._hiddenSummary:', currentConversation?._hiddenSummary);
    
    // 现在由UploadPanel组件处理显示逻辑，这里不需要做任何事情
    // 总结内容会通过props传递给UploadPanel组件
  };

  // 添加查看速览功能
  const handleViewOverview = () => {
    console.log('handleViewOverview 被调用');
    console.log('currentConversation:', currentConversation);
    console.log('currentConversation?.overview_md:', currentConversation?.overview_md);
    console.log('currentConversation?.id:', currentConversation?.id);
    
    // 现在由UploadPanel组件处理显示逻辑，这里不需要做任何事情
    // 速览内容会通过props传递给UploadPanel组件
  };

  // 轮询转写进度
  const pollTranscriptionProgress = async (transcriptionId: number, sessionKey: string) => {
    let attempts = 0;
    const maxAttempts = 200; // 最大轮询次数，防止无限轮询
    
    const poll = async (): Promise<void> => {
      try {
        attempts++;
        console.log(`轮询转写进度，第${attempts}次尝试，ID:`, transcriptionId);
        
        const response = await authenticatedApiRequest(`${API_CONFIG.baseURL}${API_ENDPOINTS.getTranscription}/${transcriptionId}`, {
          method: 'GET',
        });
        
        if (!response.success) {
          throw new Error(`获取转写详情失败: ${response.code}, ${response.message}`);
        }
        
        const data = response;
        console.log('轮询响应数据:', data);
        
        const transcriptionData = data.data || data;
        const taskStatus = transcriptionData.task_status;
        const content = transcriptionData.content || [];
        
        console.log('当前任务状态:', taskStatus, '内容长度:', content.length);
        console.log('summary_md:', transcriptionData.summary_md);
        console.log('overview_md:', transcriptionData.overview_md);
        
        // 先立即更新会话状态，确保UI能反映最新状态
        setConversations(prevConversations => {
          return prevConversations.map(conv => {
            if (conv.key === sessionKey) {
              console.log('轮询时更新会话状态:', {
                key: conv.key,
                label: conv.label,
                oldTaskStatus: conv.taskStatus,
                newTaskStatus: taskStatus,
                transcriptionId: transcriptionId
              });
              return {
                ...conv,
                taskStatus: taskStatus,
                isLocal: false // 确保始终为false，这样会话能在历史记录中显示
              };
            }
            return conv;
          });
        });
        
        // 根据任务状态更新UI
        switch (taskStatus) {
          case 'before_callback':
          case 'convert_running':
            setProcessingStatus({ message: '正在处理音频文件...', type: 'processing' });
            break;
          case 'convert_failed':
            setProcessingStatus({ message: '转写失败', type: 'error' });
            setIsProcessing(false);
            message.error('转写失败');
            return;
          case 'transcribe_pending':
          case 'transcribe_running':
            setProcessingStatus({ message: '正在转写中...', type: 'processing' });
            break;
          case 'transcribe_failed':
            setProcessingStatus({ message: '转写失败', type: 'error' });
            setIsProcessing(false);
            message.error('转写失败');
            return;
          case 'summarize_pending':
          case 'summarize_running':
            setProcessingStatus({ message: '正在生成总结...', type: 'processing' });
            break;
          case 'summarize_failed':
            setProcessingStatus({ message: '总结生成失败', type: 'error' });
            // 总结失败不中断，继续显示转写内容
            break;
          case 'done':
            setProcessingStatus({ message: '转写完成！', type: 'success' });
            setIsProcessing(false);
            message.success('文件转写完成！');
            
            // 3秒后清除状态消息
            setTimeout(() => {
              setProcessingStatus(null);
            }, 3000);
            
            // 移除转写完成后的历史记录刷新，避免不必要的列表刷新
            // setTimeout(() => {
            //   loadTranscriptionHistory('file');
            // }, 500);
            break; // 改为break，让后续的消息更新逻辑执行
        }
        
        // 会话状态已在前面更新，这里不需要重复更新
        
        // 如果有转写内容，更新会话消息（在转写运行、总结阶段或完成时都要更新）
        if (content.length > 0 && (
          taskStatus === 'transcribe_running' || 
          taskStatus === 'summarize_pending' || 
          taskStatus === 'summarize_running' || 
          taskStatus === 'summarize_failed' ||
          taskStatus === 'done'
        )) {
          console.log('更新会话转写内容，消息数量:', content.length);
          console.log('原始content数据:', content);
          
          // 将转写内容转换为消息格式
          const messages = content.map((item: any, index: number) => {
            const message = {
              id: `transcript_${transcriptionId}_${index}`,
              content: item.text || '',
              isUser: false,
              timestamp: Date.now() + index,
              speaker: item.speaker !== undefined ? item.speaker : 0,
              start_time: item.start_time,
              end_time: item.end_time
            };
            console.log('创建的消息:', message);
            return message;
          });
          
          // 更新会话消息和summary
          setConversations(prevConversations => {
            return prevConversations.map(conv => {
              if (conv.key === sessionKey) {
                return {
                  ...conv,
                  messages: messages,
                  // 如果服务器返回了summary_md，也一并更新
                  summary: transcriptionData.summary_md || conv.summary,
                  // 如果服务器返回了overview_md，也一并更新
                  overview_md: transcriptionData.overview_md || conv.overview_md,
                  taskStatus: taskStatus,
                  isLocal: false // 确保始终为false
                };
              }
              return conv;
            });
          });
        }
        
        // 在done状态时，即使没有转写内容，也要更新summary（专门处理summary_md字段）
        if (taskStatus === 'done' && transcriptionData.summary_md) {
          console.log('任务完成，更新summary_md:', transcriptionData.summary_md);
          setConversations(prevConversations => {
            return prevConversations.map(conv => {
              if (conv.key === sessionKey) {
                return {
                  ...conv,
                  summary: transcriptionData.summary_md,
                  overview_md: transcriptionData.overview_md,
                  taskStatus: taskStatus,
                  isLocal: false // 确保始终为false
                };
              }
              return conv;
            });
          });
        }
        
        // 如果任务还在进行中，继续轮询
        if (taskStatus !== 'done' && taskStatus !== 'convert_failed' && taskStatus !== 'transcribe_failed') {
          if (attempts < maxAttempts) {
            setTimeout(poll, 3000); // 3秒后继续轮询
          } else {
            console.error('轮询超时');
            setProcessingStatus({ message: '转写超时，请稍后查看结果', type: 'error' });
            setIsProcessing(false);
            message.error('转写超时，请稍后查看结果');
          }
        }
      } catch (error: any) {
        console.error('轮询转写进度失败:', error);
        if (attempts < maxAttempts) {
          setTimeout(poll, 6000); // 出错时6秒后重试
        } else {
          setProcessingStatus({ message: '获取转写进度失败', type: 'error' });
          setIsProcessing(false);
          message.error('获取转写进度失败: ' + error.message);
        }
      }
    };
    
    // 开始轮询 - 先立即检查一次状态
    poll();
  };
  
  const handleExportSummaryPDF = async (summaryContent: string) => {
    if (!summaryContent) {
      messageApi.error('没有可导出的总结内容');
      return;
    }
    
    await handleExportPDF(summaryContent, 'summary');
  };
  
  const handleExportOverviewPDF = async (overviewContent: string) => {
    if (!overviewContent) {
      messageApi.error('没有可导出的速览内容');
      return;
    }
    
    await handleExportPDF(overviewContent, 'overview');
  };
  
  // 监听tab切换，若不是录音tab则关闭录音结束等待蒙层
  useEffect(() => {
    if (activeTab !== 'recording' && isWaitingForResult) {
      setIsWaitingForResult(false);
      setWaitingProgress(0);
    }
  }, [activeTab]);
  
  return (
    <Layout style={{ height: '100vh', background: '#f5f5f5' }}>
      {contextHolder}
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        height: '100%',
        padding: '0'
      }}>
        {/* 页面标题 */}
        <PageHeader onBackClick={handleBackToMain} onMarqueeClick={handleMarqueeClick} />
        
        {/* 主体布局 */}
        <Layout style={{ 
          flex: 1, 
          background: '#fff',
          display: 'flex',
          flexDirection: 'row',
          padding: '0 24px 24px',
          position: 'relative'
        }}>
          {/* 页面访问控制蒙层 */}
          {!pageAccessible && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(255, 255, 255, 0.95)',
              zIndex: 999,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backdropFilter: 'blur(3px)'
            }}>
              <div style={{
                textAlign: 'center',
                padding: '40px',
                background: '#fff',
                borderRadius: '12px',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                border: '1px solid #f0f0f0'
              }}>
                <ExclamationCircleFilled style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: '16px' }} />
                <Title level={4} style={{ marginBottom: '8px' }}>请先阅读并同意提示</Title>
                <Text type="secondary">请在弹窗中确认同意后继续使用</Text>
              </div>
            </div>
          )}
          
          {/* 左侧菜单 */}
          <SideMenu 
            activeTab={activeTab}
            conversations={conversations}
            activeConversation={activeConversation}
            hasTranscriptData={hasTranscriptData}
            onTabChange={handleTabChange}
            onConversationChange={handleConversationChange}
            onDeleteConversation={handleDeleteTranscription}
            isRecording={isRecording}
            isProcessing={isProcessing}
            generatingSummary={generatingSummary}
          />
          
          {/* 主内容区域 */}
          <Content style={{ padding: '20px', flex: 1 }}>
            {/* 录音功能 */}
            {activeTab === 'recording' && (
              <RecordingPanel 
                isRecording={isRecording}
                isProcessing={isProcessing}
                recordTime={recordTime}
                currentMessages={currentConversation?.messages || []}
                onStartRecording={handleStartRecording}
                onStopRecording={handleStopRecording}
                onGenerateSummary={() => {
                  if (currentConversation && currentConversation.id) {
                    handleGenerateSummary('report', currentConversation.id);
                  } else {
                    message.error('请先保存当前录音记录');
                  }
                }}
                isGeneratingSummary={generatingSummary}
                onExportPDF={handleExportTranscriptPDF}
                // 添加删除功能相关props
                isHistoryRecord={!!(currentConversation?.id && (
                  activeConversation.startsWith('history_') || 
                  (currentConversation?.type === 'file' && currentConversation?.taskStatus) || // 只要有taskStatus就显示删除按钮
                  (currentConversation?.type === 'audio' && !isRecording)
                ))}
                recordId={currentConversation?.id}
                onDeleteRecord={handleDeleteTranscription}
              />
            )}
            
            {/* 上传文件功能 */}
            {activeTab === 'upload' && (
              <UploadPanel 
                isProcessing={isProcessing}
                fileList={fileList}
                currentMessages={currentConversation?.messages || []}
                onFileUpload={handleFileUpload}
                processingStatus={processingStatus?.message || ''}
                processingProgress={transcriber.current?.audioFileProcessingProgress || 0}
                decodeStatus={decodeStatus}
                decodeProgress={decodeProgress}
                onExportPDF={handleExportTranscriptPDF}
                onExportTranscriptPDF={handleExportTranscriptPDF}
                onExportSummaryPDF={handleExportSummaryPDF}
                onExportOverviewPDF={handleExportOverviewPDF}
                onViewSummary={handleViewSummary}
                onViewOverview={handleViewOverview}
                hasSummary={!!(currentConversation?.summary || currentConversation?.summary_md)}
                hasOverview={hasOverview}
                summaryContent={currentConversation?.summary || currentConversation?.summary_md || currentConversation?._hiddenSummary || ''}
                overviewContent={currentConversation?.overview_md || ''}
                // 添加删除功能相关props
                isHistoryRecord={!!(currentConversation?.id && (
                  activeConversation.startsWith('history_') || 
                  (currentConversation?.type === 'file' && currentConversation?.taskStatus) || // 只要有taskStatus就显示删除按钮
                  (currentConversation?.type === 'audio' && !isRecording)
                ))}
                recordId={currentConversation?.id}
                onDeleteRecord={handleDeleteTranscription}
                // 添加任务状态信息
                taskStatus={currentConversation?.taskStatus}
                messageApi={messageApi}
              />
            )}
            
            {/* 生成总结功能 */}
            {activeTab === 'summary' && (
              <SummaryPanel 
                summary={currentConversation?.summary || ''}
                onExport={(summary) => handleExportSummaryPDF(summary)}
                onBack={() => setActiveTab(hasTranscriptData ? 'recording' : 'upload' as TabType)}
                onGenerateSummary={handleGenerateSummary}
                isGenerating={generatingSummary}
                transcriptionRecords={conversations.map(conv => ({
                  id: conv.key,
                  title: conv.label || '未命名转写',
                  date: conv.timestamp ? formatDateTime(conv.timestamp) : '',
                  type: conv.type || (conv.fileInfo ? 'file' : 'audio')
                }))}
                onLoadRecordDetail={loadTranscriptionDetail}
                onJumpToTranscription={handleJumpToTranscription}
                onFileUpload={handleFileUpload}
              />
            )}

            {/* 翻译功能 */}
            {activeTab === 'translate' && (
              <div style={{ maxWidth: '800px', width: '100%' }}>
                <div style={{ marginBottom: '32px', display: 'flex', alignItems: 'center' }}>
                  <Title level={4} style={{ margin: 0 }}>音频翻译</Title>
                </div>
                
                <div style={{ 
                  background: '#fafafa',
                  borderRadius: '8px',
                  border: '1px solid #f0f0f0',
                  padding: '40px',
                  textAlign: 'center'
                }}>
                  <div style={{ marginBottom: '24px' }}>
                    <FileTextOutlined style={{ fontSize: '48px', color: primaryColor, marginBottom: '16px' }} />
                    <Title level={5}>翻译功能即将上线</Title>
                    <Text type="secondary">该功能正在开发中，敬请期待</Text>
                  </div>
                  
                  <div style={{ marginTop: '32px' }}>
                    <Button 
                      type="primary"
                      onClick={() => setActiveTab('recording')}
                    >
                      返回录音转写
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </Content>
        </Layout>
      </div>
      
      {/* 全局样式 */}
      <style jsx global>{`
        .ant-btn-primary {
          background-color: ${primaryColor} !important;
          border-color: ${primaryColor} !important;
          color: #333 !important;
        }
        
        .ant-btn-primary:hover {
          background-color: ${primaryColorMedium} !important;
          border-color: ${primaryColorMedium} !important;
        }
        
        .ant-btn-default:hover {
          color: ${primaryColor} !important;
          border-color: ${primaryColor} !important;
        }
        
        .ant-message-custom-content {
          display: flex;
          align-items: center;
        }
        
        .transcript-container {
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          overflow-y: auto;
          height: 400px;
          padding: 12px;
          margin-top: 16px;
          background-color: #fafafa;
          position: relative; /* 添加相对定位，以便放置滚动按钮 */
        }
        
        .transcript-container.force-visible {
          opacity: 0.99;
          z-index: 1;
          position: relative;
        }
        
        .message-bubble {
          margin-bottom: 12px;
          padding: 10px 14px;
          border-radius: 8px;
          max-width: 80%;
          word-wrap: break-word;
        }
        
        .user-message {
          background-color: ${primaryColorLight};
          align-self: flex-end;
          margin-left: auto;
        }
        
        .ai-message {
          background-color: #f0f0f0;
          align-self: flex-start;
        }
        
        .scroll-to-bottom-btn {
          position: fixed;
          bottom: 100px;
          right: 30px;
          background-color: rgba(255, 206, 57, 0.9);
          color: #333;
          border: none;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          transition: all 0.3s;
          z-index: 1000;
          display: none;
          font-size: 18px;
          font-weight: bold;
        }
        
        .scroll-to-bottom-btn:hover {
          background-color: rgba(255, 206, 57, 1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          transform: translateY(-2px);
        }
        
        .font-preload-indicator {
          position: fixed;
          bottom: 20px;
          left: 20px;
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 8px 12px;
          border-radius: 4px;
          font-size: 12px;
          z-index: 999;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: opacity 0.3s;
        }
        
        .font-preload-indicator.hidden {
          opacity: 0;
          pointer-events: none;
        }
        
        .font-preload-spinner {
          width: 12px;
          height: 12px;
          border: 2px solid transparent;
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
      
      {/* 字体预加载状态指示器 */}
      {showFontIndicator && (
        <div className="font-preload-indicator">
          <div className="font-preload-spinner"></div>
          <span>正在后台预加载字体文件...</span>
        </div>
      )}

      {/* 测试网页提示弹窗 */}
      <Modal
        title={
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            gap: '12px',
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#FFCE3A'
          }}>
            <ExclamationCircleFilled style={{ fontSize: '22px', color: '#FFCE3A' }} />
            系统使用温馨提示
          </div>
        }
        open={testWarningModalOpen}
        onOk={handleTestWarningOk}
        okText="我同意继续使用"
        centered
        closable={false}
        maskClosable={false}
        width={700}
        footer={
          <div style={{ textAlign: 'center', padding: '16px 0' }}>
            <Button
              key="ok"
              type="primary"
              onClick={handleTestWarningOk}
              style={{
                background: '#FFCE3A',
                borderColor: '#FFCE3A',
                color: '#333',
                fontWeight: 'bold',
                height: '40px',
                fontSize: '14px'
              }}
            >
              我同意继续使用
            </Button>
          </div>
        }
      >
        <div style={{ 
          padding: '20px 0',
          lineHeight: '1.6'
        }}>
          {/* 欢迎语 */}
          <div style={{
            fontSize: '16px',
            color: '#333',
            marginBottom: '16px',
            fontWeight: '500'
          }}>
            尊敬的客户：欢迎体验电牙Demo系统！
          </div>
          
          {/* 说明文字 */}
          <div style={{
            fontSize: '14px',
            color: '#666',
            marginBottom: '20px',
            lineHeight: '1.5'
          }}>
            当前产品处于测试阶段，部分功能仍在优化中。为了您更顺畅地使用，请留意以下已知问题，我们将持续修复更新：
          </div>

          {/* 问题列表 */}
          <div style={{ paddingLeft: '8px' }}>
            {[
              {
                title: '1.Demo兼容性',
                items: [
                  '暂未适配手机端，请使用 PC网页登录',
                  '推荐使用 Chrome谷歌浏览器 避免异常'
                ]
              },
              {
                title: '2.实时录音转写',
                items: [
                  '录音开始后的前 20秒为沙箱状态，可用于会议准备',
                  '结束录音后最后15秒内容不会不纳入转写'
                ]
              },
              {
                title: '3.转写进度条卡住',
                items: ['若进度条超过5分钟无变化，请重新上传文件']
              },
              {
                title: '4.其他转写异常',
                items: [
                  '上传后卡在准备页面？重试多次后问题依旧（联系客服处理）',
                  '可能出现部分内容丢失（正在修复，请重新尝试）',
                  '录音中时长突变为 "00:00"（请忽略）',
                  '若实时转写未结束，点击"生成总结"将无响应',
                  '生成新总结后，需手动刷新页面查看历史记录'
                ]
              },
              {
                title: '5.文件上传限制',
                items: ['支持mp3、wav、m4a、aac、mp4、wma、ogg、amr、flac等音频格式，文件大小≤200MB']
              },
              {
                title: '6.未开放的高阶功能',
                items: [
                  '区分对话人、实时翻译、批量上传、上传文件总结',
                  '对话模型仅开放电牙（DeepSeek/GPT系列暂不可用）'
                ]
              }
            ].map((section, sectionIndex) => (
              <div key={sectionIndex} style={{ marginBottom: '12px' }}>
                <div style={{
                  fontSize: '14px',
                  color: '#333',
                  fontWeight: '500',
                  marginBottom: '6px'
                }}>
                  {section.title}
                </div>
                {section.items.map((item, itemIndex) => (
                  <div key={itemIndex} style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: '8px',
                    marginBottom: '4px',
                    paddingLeft: '16px'
                  }}>
                    <div style={{
                      fontSize: '14px',
                      color: '#666',
                      lineHeight: '1.5'
                    }}>
                      • {item}
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </Modal>

      {/* 录音结束等待蒙层 - 只在录音页面显示 */}
      {activeTab === 'recording' && isWaitingForResult && (
        <div
          style={{
            position: 'fixed',
            right: 32,
            bottom: 32,
            zIndex: 1000,
            width: 360,
            background: '#fff',
            boxShadow: '0 4px 24px rgba(0,0,0,0.12)',
            borderRadius: 12,
            padding: '32px 24px',
            border: '1px solid #f0f0f0',
            textAlign: 'center',
            transition: 'opacity 0.2s',
            opacity: 1
          }}
        >
          <div style={{ fontSize: '18px', fontWeight: 500, marginBottom: 20, color: '#333' }}>
            录音处理中
          </div>
          <div style={{ fontSize: '14px', color: '#666', marginBottom: 30, lineHeight: '1.5' }}>
            {waitingMessage}
          </div>
          <div style={{ marginBottom: 20 }}>
            <div style={{
              width: '100%',
              height: 8,
              backgroundColor: '#f0f0f0',
              borderRadius: 4,
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${waitingProgress}%`,
                height: '100%',
                backgroundColor: primaryColor,
                borderRadius: 4,
                transition: 'width 0.3s ease'
              }} />
            </div>
          </div>
          <div style={{ fontSize: '16px', color: '#faad14', margin: '10px 0' }}>
            {`预计剩余 ${Math.max(0, 20 - Math.floor(waitingProgress / 5))} 秒`}
          </div>
          <div style={{ fontSize: '12px', color: '#999' }}>
            {Math.round(waitingProgress)}% 完成
          </div>
          <div style={{ fontSize: '12px', color: '#999', marginTop: 10 }}>
            如果服务器快速响应，将提前完成
          </div>
        </div>
      )}
      
      {/* 确认切换Modal */}
      <Modal
        title="确认切换"
        open={confirmModalOpen}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="确定切换"
        cancelText="取消"
        centered
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ExclamationCircleFilled style={{ color: '#faad14', fontSize: '16px' }} />
          <span>
            当前正在进行{pendingAction?.operationText}操作，切换{pendingAction?.type === 'tab' ? '标签' : '会话'}可能会中断当前操作。确定要继续吗？
          </span>
        </div>
      </Modal>
    </Layout>
  );
};

export default ModelExperiencePage;
