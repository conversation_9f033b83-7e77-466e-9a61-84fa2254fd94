import { message } from 'antd';
import { API_CONFIG, API_ENDPOINTS } from '@/config/api';

export interface TranscriptData {
  text: string;
  start?: number;
  end?: number;
  start_time?: number;
  end_time?: number;
  speaker?: number;
}

export interface TranscriptMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: number;
  speaker?: number; // 添加对话人字段
}

// 添加容错保存回调接口
export interface FaultToleranceCallbacks {
  onPeriodicSave?: (data: TranscriptData[]) => Promise<void>;
  onEmergencySave?: (data: TranscriptData[], reason: string) => Promise<void>;
}

// 添加解码进度回调接口
export interface DecodeProgressCallback {
  onDecodeProgress?: (status: string, progress?: number) => void;
}

class AudioTranscriber {
  private ws: WebSocket | null = null;
  private isProcessing: boolean = false;
  private isError: boolean = false;
  private chunkSize: number = 5 * 16000; // 5秒的音频数据
  
  // 添加计数器跟踪连续全0帧
  private consecutiveZeroFrames: number = 0;
  private maxConsecutiveZeroFrames: number = 5; // 连续5帧全0后触发恢复
  private isRecoveringAudioProcessor: boolean = false;
  
  // WebSocket 相关
  private wsUrl: string = 'wss://ai-api.dianyaai.com/api/realtime_asr/ws/huoshan';
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 2000; // 初始重连延迟2秒
  
  // 录音相关
  private mediaRecorder: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;
  private recordingStream: MediaStream | null = null;
  private isRecording: boolean = false;
  private recordingStartTime: number = 0;
  private recordingTimer: NodeJS.Timeout | null = null;
  private audioProcessor: ScriptProcessorNode | null = null;
  private recordingChunks: Blob[] = [];
  
  // 转写内容相关
  private transcriptContent: string = '';
  private lastTranscriptTime: number = 0;
  private timeThreshold: number = 30000; // 30秒的时间阈值，用于分段
  
  private data: TranscriptData[] = [];
  private onTranscriptUpdate: ((data: TranscriptData) => void) | null = null;
  private onStatusChange: ((status: string, type: 'processing' | 'success' | 'error') => void) | null = null;
  private onRecordTimeUpdate: ((time: string) => void) | null = null;
  private onProcessingComplete: (() => void) | null = null;
  
  // 锁屏和恢复相关
  private visibilityChangeHandler: (() => void) | null = null;
  private wasRecordingBeforePause: boolean = false;
  private wasProcessingBeforePause: boolean = false;
  private audioFileBeingProcessed: File | null = null;
  public audioFileProcessingProgress: number = 0;
  private audioDuration: number = 0; // 存储音频总时长（秒）
  private hasStartedProgress: boolean = false; // 标记是否已开始显示进度
  private resumeTimeout: NodeJS.Timeout | null = null;
  private progressUpdateTimeout: NodeJS.Timeout | null = null;
  private reconnectLastTime: number = 0; // 重连前的最后时间点
  
  // 添加新属性跟踪回调执行状态
  private processingCompleteExecuted: boolean = false;

  // 容错机制相关属性
  private faultToleranceCallbacks: FaultToleranceCallbacks = {};
  private periodicSaveTimer: NodeJS.Timeout | null = null;
  private periodicSaveInterval: number = 30000; // 30秒定期保存一次
  private lastSaveTime: number = 0;
  private lastSavedDataLength: number = 0;
  private emergencySaveTriggered: boolean = false;

  private onDecodeProgress: ((status: string, progress?: number) => void) | null = null;

  // 添加WebSocket结果回调
  private onWebSocketResultCallback: (() => void) | null = null;

  constructor() {
    // 添加页面可见性变化监听
    this.setupVisibilityChangeListener();
    // 添加页面卸载监听，用于紧急保存
    this.setupBeforeUnloadListener();
  }

  // 设置回调函数
  setOnTranscriptUpdate(callback: (data: TranscriptData) => void) {
    this.onTranscriptUpdate = callback;
  }

  setOnStatusChange(callback: (status: string, type: 'processing' | 'success' | 'error') => void) {
    this.onStatusChange = callback;
  }

  setOnRecordTimeUpdate(callback: (time: string) => void) {
    this.onRecordTimeUpdate = callback;
  }

  setOnProcessingComplete(callback: () => void) {
    this.onProcessingComplete = callback;
    // 重置回调执行状态
    this.processingCompleteExecuted = false;
  }

  setOnDecodeProgress(callback: (status: string, progress?: number) => void) {
    this.onDecodeProgress = callback;
  }

  // 设置WebSocket结果回调
  setOnWebSocketResult(callback: () => void): void {
    this.onWebSocketResultCallback = callback;
  }

  private showStatus(message: string, type: 'processing' | 'success' | 'error' = 'processing') {
    console.log(`状态更新: ${message} (${type})`);
    if (this.onStatusChange) {
      this.onStatusChange(message, type);
    }
  }

  async initWebSocket(): Promise<void> {
    // 如果正在录音，必须建立WebSocket连接
    // 如果不在处理状态且没有暂停处理，且不在录音状态，则不需要建立WebSocket连接
    if (!this.isProcessing && !this.wasProcessingBeforePause && !this.isRecording) {
      return;
    }
    
    // 如果已有活跃的WebSocket连接，则直接返回
    if (this.ws && (this.ws.readyState === WebSocket.CONNECTING || this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    // 如果有之前的WebSocket连接，先关闭
    if (this.ws) {
      try {
        this.ws.close();
      } catch (e) {
        console.error('关闭旧WebSocket连接时出错:', e);
      }
      this.ws = null;
    }

    try {
      await this.connectWebSocket();
      this.reconnectAttempts = 0; // 重置重连次数
    } catch (error) {
      console.error('WebSocket 连接失败:', error);
      this.handleReconnect();
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1); // 指数退避
      
      // 仅记录日志，不显示状态，避免覆盖"正在解析文件中..."
      console.log(`正在尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.initWebSocket();
      }, delay);
    } else {
      // 如果此时正在处理文件，显示错误信息
      if (this.isProcessing && this.audioFileBeingProcessed && !this.isRecording) {
        this.showStatus('转写服务连接失败，请重试', 'error');
        this.isProcessing = false;
      }
      
      // 如果是录音状态，显示连接失败但不停止录音
      if (this.isRecording) {
        console.log('录音状态下WebSocket重连失败，但继续录音');
        this.showStatus('网络连接不稳定，转写可能中断', 'error');
      }
    }
  }
  
  async checkMicrophonePermission(): Promise<boolean> {
    try {
      // 检查浏览器是否支持 MediaRecorder
      if (!window.MediaRecorder) {
        this.showStatus('您的浏览器不支持录音功能，请使用最新版本的Chrome、Firefox或Safari', 'error');
        return false;
      }
      
      // 检查麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // 立即停止流，只是为了检查权限
      return true;
    } catch (error) {
      console.error('麦克风权限检查失败:', error);
      this.showStatus('无法访问麦克风，请确保已授予麦克风权限', 'error');
      return false;
    }
  }
  
  async startRecording(): Promise<void> {
    try {
      this.showStatus('正在准备录音...', 'processing');
      
      // 清除之前的转写内容
      this.clearTranscript();
      
      // 重置容错相关状态
      this.lastSaveTime = 0;
      this.lastSavedDataLength = 0;
      this.emergencySaveTriggered = false;

      this.isProcessing = true;
      
      // 初始化WebSocket连接
      await this.initWebSocket();
      
      // 获取麦克风流
      this.recordingStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      // 创建音频上下文
      // 注意：Firefox 不支持在 createMediaStreamSource 时使用不同采样率的 AudioContext
      // 所以先使用默认采样率创建 AudioContext，然后在处理音频数据时进行重采样
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // 创建音频源节点
      const sourceNode = this.audioContext.createMediaStreamSource(this.recordingStream);
      
      // 创建处理器节点
      const processorNode = this.audioContext.createScriptProcessor(4096, 1, 1);
      
      // 设置音频处理回调
      processorNode.onaudioprocess = this.createAudioProcessHandler();
      
      // 连接节点
      sourceNode.connect(processorNode);
      processorNode.connect(this.audioContext.destination);
      
      this.audioProcessor = processorNode;
      
      // 设置录音状态
      this.isRecording = true;
      this.isProcessing = true;
      this.recordingStartTime = Date.now();
      
      // 启动定期保存机制
      this.startPeriodicSave();
      
      // 更新录音时间
      this.recordingTimer = setInterval(() => {
        // 在音频处理器恢复过程中，跳过计时器更新以避免跳跃
        if (this.isRecoveringAudioProcessor) {
          console.log('音频处理器恢复中，跳过计时器更新');
          return;
        }
        
        // 确保录音状态和开始时间有效
        if (!this.isRecording || this.recordingStartTime <= 0) {
          console.warn('录音状态或开始时间无效，跳过计时器更新');
          return;
        }
        
        const elapsedTime = Date.now() - this.recordingStartTime;
        const minutes = Math.floor(elapsedTime / 60000);
        const seconds = Math.floor((elapsedTime % 60000) / 1000);
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        if (this.onRecordTimeUpdate) {
          this.onRecordTimeUpdate(timeString);
        }
      }, 1000);
      
      console.log('录音已成功启动', {
        audioContextSampleRate: this.audioContext.sampleRate,
        streamSampleRate: this.recordingStream.getAudioTracks()[0]?.getSettings?.()?.sampleRate || 'unknown',
        isFirefox: navigator.userAgent.includes('Firefox')
      });
      
      this.showStatus('正在实时转写...', 'processing');
    } catch (error: any) {
      console.error('开始录音时出错:', error);
      this.showStatus('开始录音失败: ' + error.message, 'error');
      this.isRecording = false;
      
      // 停止定期保存
      this.stopPeriodicSave();
    }
  }
  
  stopRecording(): void {
    if (!this.isRecording) return;
    
    // 停止定期保存机制
    this.stopPeriodicSave();
    
    // 在停止录音前，执行最后一次保存
    if (this.data.length > 0) {
      this.triggerEmergencySave('录音停止');
    }
    
    // 停止录音计时器
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
    
    // 只有在真正停止录音时才重置录音时间显示
    // 不要在音频处理器恢复过程中重置时间
    if (this.onRecordTimeUpdate && !this.isRecoveringAudioProcessor) {
      this.onRecordTimeUpdate('00:00');
    }
    
    // 停止音频处理
    if (this.audioProcessor) {
      this.audioProcessor.disconnect();
      this.audioProcessor = null;
    }
    
    // 停止媒体流
    if (this.recordingStream) {
      this.recordingStream.getTracks().forEach(track => track.stop());
      this.recordingStream = null;
    }
    
    // 关闭音频上下文
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    // 发送结束信号并等待服务器返回最终结果
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('发送结束信号');
      this.ws.send(JSON.stringify({ "end": true }));
      
      // 设置10秒超时，如果服务器没有关闭连接，则手动关闭
      setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          console.log('等待服务器响应超时，手动关闭WebSocket连接');
          this.ws.close();
          
          // 如果还有WebSocket结果回调未触发，这里触发它
          if (this.onWebSocketResultCallback) {
            this.onWebSocketResultCallback();
            this.onWebSocketResultCallback = null;
          }
        }
      }, 10000);
    } else {
      console.warn('发送结束信号失败，WebSocket不可用');
    }
    
    // 更新状态
    this.isRecording = false;
    this.showStatus('录音已停止，正在完成转写...', 'processing');
  }

  clearTranscript(): void {
    console.log('清理转写内容');
    this.transcriptContent = '';
    this.lastTranscriptTime = 0;
    this.data = [];
  }

  private appendTranscript(data: TranscriptData): void {
    // 清除进度更新超时计时器，因为已经收到了新数据
    if (this.progressUpdateTimeout) {
      clearTimeout(this.progressUpdateTimeout);
      this.progressUpdateTimeout = null;
    }
    
    // 确保text字段存在
    if (!data.text) {
      console.warn('收到的转写数据缺少text字段');
      return;
    }
    
    const text = data.text.trim();
    if (!text) {
      console.warn('收到的转写文本为空');
      return;
    }
    // 解析时间戳
    const startTime = data.start || data.start_time;
    const endTime = data.end || data.end_time;
    
    if (endTime !== undefined) {
      // 始终检查是否需要调整时间戳，无论是否在恢复处理状态
      let adjustedEndTime = endTime;
      
      // 检查时间戳是否需要调整的条件：
      // 1. 有重连时间点记录
      // 2. 当前收到的endTime比重连时间点小很多
      // 3. endTime相对较小（小于之前记录的最后时间点的一半）
      if (this.reconnectLastTime > 0 && 
          endTime < this.reconnectLastTime && 
          endTime < (this.reconnectLastTime / 2)) {
        // 累加重连前的最后时间点，以保持进度连续
        adjustedEndTime = this.reconnectLastTime + endTime;
        console.log('检测到WebSocket重连后时间戳重置，调整end_time:', 
                    endTime, '->', adjustedEndTime, 
                    '(reconnectLastTime:', this.reconnectLastTime, ')');
      }
      
      // 更新最后转写时间点
      if (adjustedEndTime > (this.lastTranscriptTime || 0)) {
        this.lastTranscriptTime = adjustedEndTime;
        
        // 如果有音频总时长，根据end_time计算进度
        if (this.audioDuration > 0 && adjustedEndTime > 0) {
          // 将end_time从毫秒转换为秒
          const endTimeInSeconds = adjustedEndTime / 1000;
          
          // 进度计算，确保能达到100%
          // 如果超过总时长的95%，则按比例放大到95%-100%之间
          let progress;
          const rawProgress = (endTimeInSeconds / this.audioDuration) * 100;
          
          if (rawProgress >= 95) {
            // 在95%-100%之间按比例缩放
            const remaining = 100 - 95; // 剩余的5%
            const overProgress = rawProgress - 95; // 超出95%的部分
            const maxRemaining = this.audioDuration * 0.05; // 最大剩余时间，总时长的5%
            const factor = overProgress / maxRemaining; // 超出部分占最大剩余时间的比例
            progress = Math.min(95 + remaining * factor, 100); // 缩放到95%-100%之间
          } else {
            progress = rawProgress;
          }
          
          progress = Math.min(Math.floor(progress), 100);
          
          // 格式化当前解析到的时间点
          const minutes = Math.floor(endTimeInSeconds / 60);
          const seconds = Math.floor(endTimeInSeconds % 60);
          const currentTimeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
          
          // 格式化总时长
          const totalMinutes = Math.floor(this.audioDuration / 60);
          const totalSeconds = Math.floor(this.audioDuration % 60);
          const totalTimeString = `${totalMinutes.toString().padStart(2, '0')}:${totalSeconds.toString().padStart(2, '0')}`;
          
          // 组合时间显示格式为 "当前时长/总时长"
          const timeDisplay = `${currentTimeString}/${totalTimeString}`;
          
          this.audioFileProcessingProgress = progress;
          this.hasStartedProgress = true; // 标记已开始显示进度
          
          // 如果进度达到100%，显示转写完成
          if (progress === 100) {
            this.showStatus('转写完成', 'success');
            
            // 使用受保护的方法执行回调，确保只执行一次
            this.executeProcessingCompleteOnce();
            
            // 处理完成时重置状态
            setTimeout(() => {
              this.isProcessing = false;
              this.reconnectLastTime = 0;
            }, 500);
          } else {
            // 恢复解析后，应该返回显示正在解析音频
            this.showStatus(`正在解析音频：${progress}%（${timeDisplay}）`, 'processing');
          }
          
          console.log('进度更新:', progress, '%', timeDisplay);
        }
      }
    } else if (startTime) {
      // 如果没有结束时间但有开始时间，也可以考虑更新最后的时间点
      // 这是一个后备方案，因为有些转写可能只有开始时间
      let adjustedStartTime = startTime;
      
      // 同样考虑重连情况，条件与处理end_time相同
      if (this.reconnectLastTime > 0 && 
          startTime < this.reconnectLastTime && 
          startTime < (this.reconnectLastTime / 2)) {
        adjustedStartTime = this.reconnectLastTime + startTime;
        console.log('检测到WebSocket重连后时间戳重置，调整start_time:', 
                    startTime, '->', adjustedStartTime);
      }
      
      if (adjustedStartTime > (this.lastTranscriptTime || 0)) {
        this.lastTranscriptTime = adjustedStartTime;
      }
    }
    
    // 调用回调函数通知UI更新
    if (this.onTranscriptUpdate) {
      // 确保转发给UI的数据也包含正确的时间戳和speaker信息
      const updatedData = {
        ...data,
        start: startTime,
        end: endTime,
        speaker: data.speaker // 确保speaker信息被传递
      };
      this.onTranscriptUpdate(updatedData);
    }
    
    // 保存数据以便后续生成总结
    // 确保保存的数据包含正确的时间字段和speaker信息
    const normalizedData = {
      text: data.text,
      start: startTime,
      end: endTime,
      speaker: data.speaker
    };
    this.data.push(normalizedData);
  }

  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        console.log("WebSocket连接中...");
        this.ws = new WebSocket(this.wsUrl);
        
        // 设置超时
        const timeoutId = setTimeout(() => {
          if (this.ws && this.ws.readyState !== WebSocket.OPEN) {
            console.log('WebSocket连接超时');
            this.ws.close();
            reject(new Error('WebSocket连接超时'));
          }
        }, 10000); // 10秒超时
        
        this.ws.onopen = () => {
          clearTimeout(timeoutId);
          console.log("WebSocket连接已建立");
          resolve();
        };
        
        this.ws.onerror = (error) => {
          clearTimeout(timeoutId);
          console.error("WebSocket连接错误:", error);
          
          // 如果是在录音状态下发生错误，触发紧急保存
          if (this.isRecording && this.data.length > 0) {
            this.triggerEmergencySave('WebSocket连接错误');
          }
          
          reject(error);
        };
        
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('收到WebSocket消息:', data);
            
            if (data.type === "asr_result" && data.data) {
              // 检查并记录时间戳字段，便于调试
              if (data.data.end_time !== undefined && data.data.end_time % 10000 === 0) {
                console.log('收到时间戳:', data.data.end_time);
              }
              
              // 确保数据中的字段格式一致
              const normalizedData = {
                ...data.data,
                // 明确设置end和start字段
                end: data.data.end || data.data.end_time,
                start: data.data.start || data.data.start_time
              };
              
              // 检测是否发生了时间戳重置（断开重连后时间从0开始）
              const endTime = normalizedData.end;
              const startTime = normalizedData.start;
              
              // 改进时间戳重置检测逻辑：
              // 1. 确保之前有较大的时间戳记录
              // 2. 当前收到的时间戳异常小（相对于最后记录的时间戳）
              // 3. 时间差异显著（超过5秒）表明可能是重置而不是正常连续的小值
              if (this.lastTranscriptTime > 5000 && 
                  endTime !== undefined && 
                  endTime < (this.lastTranscriptTime / 2) &&
                  (this.lastTranscriptTime - endTime) > 5000) {
                console.log('检测到时间戳重置，可能是断开重连后从0开始计时');
                console.log('当前时间戳:', endTime, '最后记录的时间戳:', this.lastTranscriptTime);
                
                // 记录重连时的最后时间点，用于后续计算实际进度
                if (!this.reconnectLastTime) {
                  this.reconnectLastTime = this.lastTranscriptTime;
                  console.log('保存重连前的最后时间点:', this.reconnectLastTime);
                }
              }
              
              // 追加转写内容
              this.appendTranscript(normalizedData);
            } else if (data.type === "error") {
              console.error('收到WebSocket错误:', data);
              
              // 检查是否是超时错误
              const errorMessage = data.data?.message || '';
              const isTimeoutError = 
                (errorMessage.includes("timeout") || 
                 errorMessage.includes("Timeout")) ||
                (data.data?.error_type === "error" && 
                 errorMessage.includes("[Timeout waiting next packet]"));
              
              if (isTimeoutError) {
                console.log('检测到超时错误，可能是由于锁屏导致');
                console.log('当前最后转写时间点:', this.lastTranscriptTime);
                
                // 触发紧急保存
                if (this.isRecording && this.data.length > 0) {
                  this.triggerEmergencySave('WebSocket超时错误');
                }
                
                // 如果已有进度显示，保持当前进度，否则显示"检测到连接中断"
                if (!this.hasStartedProgress) {
                  this.showStatus("检测到连接中断，准备恢复...", "processing");
                }
                
                // 关闭当前连接
                if (this.ws) {
                  try {
                    this.ws.close();
                  } catch (e) {
                    console.error('关闭WebSocket时出错:', e);
                  }
                  this.ws = null;
                }
                
                // 如果是录音状态，直接尝试重连，不设置暂停状态
                if (this.isRecording) {
                  console.log('录音状态下检测到超时，立即尝试重连');
                  setTimeout(() => {
                    this.initWebSocket();
                  }, 1000);
                }
              } else {
                // 其他错误，触发紧急保存
                if (this.isRecording && this.data.length > 0) {
                  this.triggerEmergencySave('WebSocket其他错误: ' + errorMessage);
                }
                
                // 其他错误，但如果有进度显示，也保持当前进度
                if (!this.hasStartedProgress) {
                  this.showStatus("转写错误：" + (data.data?.message || "未知错误"), "error");
                }
                this.isError = true;
              }
            } else if (data.type === "stop") {
              // 服务端通知停止转写
              console.log("服务端通知停止转写");
              this.triggerEmergencySave('服务端停止信号');
              
              // 触发WebSocket结果回调
              if (this.onWebSocketResultCallback) {
                this.onWebSocketResultCallback();
                this.onWebSocketResultCallback = null; // 清除回调，避免重复调用
              }
              
              // 在清理文件状态前，先判断是否需要紧急保存
              // 只有在录音状态下才执行紧急保存，文件处理完成不需要紧急保存
              if (this.isRecording && this.data.length > 0) {
                this.triggerEmergencySave('服务端通知停止');
              }
              
              // 显示转写完成
              this.showStatus('转写完成', 'success');
              
              // 使用受保护的方法执行回调，确保只执行一次
              this.executeProcessingCompleteOnce();
              
              // 重置处理状态
              this.isProcessing = false;
              this.wasProcessingBeforePause = false; // 确保不会自动重连
              this.reconnectLastTime = 0;
              
              // 清理文件处理状态
              this.audioFileBeingProcessed = null;
              
              // 停止定期保存
              this.stopPeriodicSave();
              
              // 关闭WebSocket连接
              if (this.ws) {
                this.ws.close();
                this.ws = null;
              }
            }
          } catch (error) {
            console.error("处理WebSocket消息时出错:", error, event.data);
            
            // 只有在录音状态下处理消息出错才触发紧急保存
            if (this.isRecording && this.data.length > 0) {
              this.triggerEmergencySave('处理WebSocket消息时出错');
            }
          }
        };
        
        this.ws.onclose = (event) => {
          clearTimeout(timeoutId);
          console.log("WebSocket连接已关闭:", event.code, event.reason);
          
          // 只有在录音状态下的异常关闭才触发紧急保存
          // 文件处理的正常关闭不需要紧急保存
          if (event.code !== 1000 && event.code !== 1001 && this.isRecording && this.data.length > 0) {
            this.triggerEmergencySave('WebSocket异常关闭');
          }
          
          // 如果是录音状态下的异常关闭，尝试重连
          if (this.isRecording && event.code !== 1000 && event.code !== 1001) {
            console.log('录音状态下WebSocket异常关闭，尝试重连');
            this.handleReconnect();
            return;
          }
          
          // 仅在非暂停状态下处理连接关闭
          if (this.isProcessing && !this.wasProcessingBeforePause) {
            if (!this.isError) {
              // 检查是否进度已达到100%
              if (this.audioFileProcessingProgress === 100) {
                this.showStatus('转写完成', 'success');
                
                // 使用受保护的方法执行回调，确保只执行一次
                this.executeProcessingCompleteOnce();
              }
              // 如果已有进度显示但未达到100%，不清除进度，等待新的数据到来
              else if (!this.hasStartedProgress) {
                this.showStatus('转写完成', 'success');
                
                // 使用受保护的方法执行回调，确保只执行一次
                this.executeProcessingCompleteOnce();
              }
            }
            this.isProcessing = false;
            
            // 清理文件处理状态
            this.audioFileBeingProcessed = null;
            
            // 处理完成时重置reconnectLastTime
            if (!this.wasProcessingBeforePause) {
              this.reconnectLastTime = 0;
              console.log('处理完成，重置reconnectLastTime');
            }
          } else if (event.code !== 1000 && event.code !== 1001) {
            // 非正常关闭，尝试重连
            this.handleReconnect();
          }
        };
      } catch (error) {
        console.error("创建WebSocket实例时出错:", error);
        
        // 只有在录音状态下创建WebSocket出错才触发紧急保存
        if (this.isRecording && this.data.length > 0) {
          this.triggerEmergencySave('创建WebSocket实例时出错');
        }
        
        reject(error);
      }
    });
  }

  async generateSummary(): Promise<string> {
    console.log('开始生成总结');
    
    if (this.isProcessing) {
      console.warn('正在处理其他音频，请稍后再试');
      throw new Error('正在处理其他音频，请稍后再试');
    }

    if (this.data.length === 0) {
      console.warn('没有可用的转写内容来生成总结');
      throw new Error('没有可用的转写内容来生成总结');
    }

    // 设置处理中状态
    this.isProcessing = true;
    this.showStatus('正在生成总结...', 'processing');

    try {
      // 这个方法已不再直接调用API，而是通过generateSummaryFromData方法
      const transcriptData = this.data;
      return await this.generateSummaryFromData(transcriptData);
    } catch (error) {
      console.error('生成总结失败:', error);
      this.showStatus('生成总结失败，请稍后再试', 'error');
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  // 从指定的转录数据生成总结
  async generateSummaryFromData(transcriptData: TranscriptData[], templateType: string = 'report'): Promise<string> {
    console.log('从指定数据开始生成总结，模板类型:', templateType);
    
    if (this.isProcessing) {
      console.warn('正在处理其他音频，请稍后再试');
      throw new Error('正在处理其他音频，请稍后再试');
    }

    if (!transcriptData || transcriptData.length === 0) {
      console.warn('没有可用的转写内容来生成总结');
      throw new Error('没有可用的转写内容来生成总结');
    }

    // 设置处理中状态
    this.isProcessing = true;
    this.showStatus('正在生成总结...', 'processing');

    try {
      // 这里需要从外部传入transcription_id
      throw new Error('请使用generateSummaryById方法并提供转写ID');
    } catch (error) {
      console.error('生成总结失败:', error);
      this.showStatus('生成总结失败，请稍后再试', 'error');
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }
  
  // 通过转写ID生成总结的新方法
  async generateSummaryById(transcriptionId: number, name: string = '总结'): Promise<string> {
    console.log('通过ID生成总结, ID:', transcriptionId, '名称:', name);
    
    if (this.isProcessing) {
      console.warn('正在处理其他音频，请稍后再试');
      throw new Error('正在处理其他音频，请稍后再试');
    }

    // 设置处理中状态
    this.isProcessing = true;
    this.showStatus('正在生成总结...', 'processing');

    // 重试配置
    const maxRetries = 3;
    const baseDelay = 1000; // 基础延迟1秒

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // 获取token
        const token = localStorage.getItem('dy-token');
        if (!token) {
          throw new Error('未找到登录凭证，请重新登录');
        }
        
        const req_body = {
          name: name,
          transcription_id: transcriptionId
        };

        console.log(`发送总结请求 (尝试 ${attempt + 1}/${maxRetries + 1}):`, req_body);
        
        // 使用新的API接口
        const response = await fetch(`${API_CONFIG.baseURL}${API_ENDPOINTS.createSummary}`, {
          method: 'POST',
          body: JSON.stringify(req_body),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
        });

        // 如果是500错误且还有重试机会，则进行重试
        if (response.status === 500 && attempt < maxRetries) {
          console.warn(`服务器返回500错误，将在 ${baseDelay * (attempt + 1)}ms 后进行第 ${attempt + 2} 次尝试`);
          this.showStatus(`服务器繁忙，正在重试 (${attempt + 1}/${maxRetries})...`, 'processing');
          
          // 等待递增的延迟时间后重试
          await new Promise(resolve => setTimeout(resolve, baseDelay * (attempt + 1)));
          continue;
        }

        if (!response.ok) {
          console.error('API响应错误:', response.status, response.statusText);
          
          // 如果是最后一次尝试或非500错误，抛出错误
          if (response.status === 500) {
            throw new Error(`服务器错误，已重试 ${maxRetries} 次仍然失败`);
          } else {
            throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();
        console.log('总结生成成功:', data);
        this.showStatus('总结生成成功', 'success');
        
        // 从API响应中提取content字段
        if (data.code === 200 && data.data && data.data.content) {
          // 成功时立即重置处理状态
          this.isProcessing = false;
          return data.data.content;
        } else {
          console.warn('API返回数据格式不符合预期:', data);
          // 成功时立即重置处理状态
          this.isProcessing = false;
          return data.data?.content || '总结生成成功，但内容为空';
        }
      } catch (error) {
        // 如果是最后一次尝试，或者不是网络/服务器错误，直接抛出
        if (attempt === maxRetries || !(error instanceof Error && (error.message.includes('fetch') || error.message.includes('服务器错误')))) {
          console.error('生成总结失败:', error);
          this.showStatus('生成总结失败，请稍后再试', 'error');
          // 失败时重置处理状态
          this.isProcessing = false;
          throw error;
        }
        
        // 如果不是最后一次尝试且是网络相关错误，继续重试
        console.warn(`生成总结失败 (尝试 ${attempt + 1}/${maxRetries + 1}):`, error);
        this.showStatus(`网络错误，正在重试 (${attempt + 1}/${maxRetries})...`, 'processing');
        
        // 等待递增的延迟时间后重试
        await new Promise(resolve => setTimeout(resolve, baseDelay * (attempt + 1)));
      }
    }

    // 这行代码理论上不会执行到，但为了类型安全添加
    this.isProcessing = false;
    throw new Error('生成总结失败，已达到最大重试次数');
  }

  getTranscriptData(): TranscriptData[] {
    return this.data;
  }

  isCurrentlyProcessing(): boolean {
    return this.isProcessing;
  }

  isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  // 设置页面可见性变化监听
  private setupVisibilityChangeListener(): void {
    this.visibilityChangeHandler = () => {
      this.handleVisibilityChange();
    };
    
    // 添加页面可见性变化事件监听
    document.addEventListener('visibilitychange', this.visibilityChangeHandler);
  }
  
  // 处理页面可见性变化
  private handleVisibilityChange(): void {
    if (document.visibilityState === 'hidden') {
      
      // 录音不需要处理页面可见性变化，因为录音在后台仍然可以工作
      // 只处理文件处理的情况
      if (!this.isRecording && this.isProcessing) {
        this.pauseProcessing();
      } else if (this.isRecording) {
        // 记录当前正在录音，以便在恢复时检查录音状态
        this.wasRecordingBeforePause = true;
      }
    } else if (document.visibilityState === 'visible') {
      // 清除可能存在的恢复超时
      if (this.resumeTimeout) {
        clearTimeout(this.resumeTimeout);
        this.resumeTimeout = null;
      }
      
      // 设置一个短暂的延迟，以确保浏览器和系统完全恢复
      this.resumeTimeout = setTimeout(async () => {
        // 先检查录音状态，恢复音频处理器
        if (this.isRecording) {
          // 保存当前录音状态，防止在恢复过程中被重置
          const currentRecordingStartTime = this.recordingStartTime;
          const wasRecording = this.isRecording;
          
          // 重置全0帧计数
          this.consecutiveZeroFrames = 0;
          
          await this.checkAndRestoreAudioProcessor();
          
          // 确保录音状态和计时器没有被意外重置
          if (wasRecording) {
            this.isRecording = true;
            this.recordingStartTime = currentRecordingStartTime;
          }
          
          // 检查WebSocket连接状态，但不发送JSON心跳包
          if (this.ws) {
            if (this.ws.readyState !== WebSocket.OPEN) {

              // 重新建立WebSocket连接
              try {
                await this.initWebSocket();
              } catch (err) {
              }
            } else {
            }
          } else {
            try {
              await this.initWebSocket();
            } catch (err) {
            }
          }
        }
      }, 1000);
    }
  }
  
  // 暂停处理
  private pauseProcessing(): void {
    console.log('暂停处理，当前状态:', { 
      isRecording: this.isRecording,
      isProcessing: this.isProcessing,
      lastTranscriptTime: this.lastTranscriptTime,
      hasStartedProgress: this.hasStartedProgress // 记录进度显示状态
    });
    
    // 确保有有效的转写时间点，以便恢复时使用
    if (this.lastTranscriptTime > 0) {
      console.log('暂停前的最后转写时间点:', this.lastTranscriptTime);
      
      // 确保reconnectLastTime被设置，即使之前没有检测到时间戳重置
      this.reconnectLastTime = this.lastTranscriptTime;
      console.log('在暂停时明确设置重连时间点:', this.reconnectLastTime);
    } else {
      console.warn('暂停前没有有效的转写时间点，恢复可能从头开始');
    }
    
    // 保存当前状态
    this.wasProcessingBeforePause = this.isProcessing;
    this.wasRecordingBeforePause = this.isRecording;
    
    // 如果WebSocket连接存在，关闭它
    if (this.ws) {
      console.log('关闭WebSocket连接以便在恢复时重新建立');
      try {
        this.ws.close(1000, 'User paused recording');
      } catch (e) {
        console.error('关闭WebSocket时出错:', e);
      }
      this.ws = null;
    }
    
    // 不停止AudioContext和媒体流，只是标记状态
    // 这样我们可以在恢复时重新启动
  }

  // 添加一个受保护的执行回调方法，确保只执行一次
  private executeProcessingCompleteOnce() {
    if (this.processingCompleteExecuted) {
      console.log('处理完成回调已执行过，跳过重复执行');
      return;
    }
    
    if (this.onProcessingComplete) {
      console.log('执行处理完成回调');
      this.processingCompleteExecuted = true;
      this.onProcessingComplete();
    }
    
    // 清理文件处理状态
    if (this.audioFileBeingProcessed) {
      console.log('清理文件处理状态');
      this.audioFileBeingProcessed = null;
    }
  }

  // 销毁方法，用于清理资源
  destroy(): void {
    console.log('销毁AudioTranscriber实例');
    
    // 在销毁前，如果有未保存的数据，触发紧急保存
    if (this.data.length > 0 && (this.isRecording || this.isProcessing)) {
      this.triggerEmergencySave('实例销毁');
    }
    
    // 停止录音
    if (this.isRecording) {
      this.stopRecording();
    }
    
    // 停止定期保存
    this.stopPeriodicSave();
    
    // 清理文件处理状态
    this.audioFileBeingProcessed = null;
    
    // 关闭WebSocket连接
    if (this.ws) {
      try {
        this.ws.close();
      } catch (e) {
        console.error('关闭WebSocket时出错:', e);
      }
      this.ws = null;
    }
    
    // 移除页面可见性变化监听器
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler);
      this.visibilityChangeHandler = null;
    }
    
    // 清除超时
    if (this.resumeTimeout) {
      clearTimeout(this.resumeTimeout);
      this.resumeTimeout = null;
    }
    
    // 清除进度更新超时
    if (this.progressUpdateTimeout) {
      clearTimeout(this.progressUpdateTimeout);
      this.progressUpdateTimeout = null;
    }
    
    // 重置重连时间点
    this.reconnectLastTime = 0;
    
    // 清除录音计时器
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
    
    // 清除回调函数
    this.onStatusChange = null;
    this.onTranscriptUpdate = null;
    this.onProcessingComplete = null;
    this.onRecordTimeUpdate = null;
    this.onWebSocketResultCallback = null;
    this.faultToleranceCallbacks = {};
    
    // 重置回调执行状态
    this.processingCompleteExecuted = false;
    
    console.log('AudioTranscriber资源已清理');
  }

  // 设置容错回调函数
  setFaultToleranceCallbacks(callbacks: FaultToleranceCallbacks) {
    this.faultToleranceCallbacks = callbacks;
    console.log('已设置容错回调函数');
  }

  // 启动定期保存机制
  private startPeriodicSave(): void {
    if (this.periodicSaveTimer) {
      clearInterval(this.periodicSaveTimer);
    }

    this.periodicSaveTimer = setInterval(() => {
      this.performPeriodicSave();
    }, this.periodicSaveInterval);

    console.log(`已启动定期保存机制，间隔: ${this.periodicSaveInterval / 1000}秒`);
  }

  // 停止定期保存机制
  private stopPeriodicSave(): void {
    if (this.periodicSaveTimer) {
      clearInterval(this.periodicSaveTimer);
      this.periodicSaveTimer = null;
      console.log('已停止定期保存机制');
    }
  }

  // 执行定期保存
  private async performPeriodicSave(): Promise<void> {
    // 只有在录音状态下且不是文件处理时才进行定期保存
    // 增加更严格的检查条件
    if (!this.isRecording || 
        this.data.length === 0 || 
        this.audioFileBeingProcessed !== null ||
        this.isProcessing && !this.isRecording) {
      console.log('跳过定期保存 - 录音状态:', this.isRecording, '数据长度:', this.data.length, '文件处理:', this.audioFileBeingProcessed !== null, '处理状态:', this.isProcessing);
      return;
    }

    // 检查是否有新的转写内容需要保存
    const currentTime = Date.now();
    const hasNewData = this.data.length > this.lastSavedDataLength;
    const timeSinceLastSave = currentTime - this.lastSaveTime;

    if (hasNewData && timeSinceLastSave >= this.periodicSaveInterval) {
      console.log(`执行定期保存，当前转写条数: ${this.data.length}，上次保存: ${this.lastSavedDataLength}`);
      
      try {
        if (this.faultToleranceCallbacks.onPeriodicSave) {
          await this.faultToleranceCallbacks.onPeriodicSave([...this.data]);
          this.lastSaveTime = currentTime;
          this.lastSavedDataLength = this.data.length;
          console.log('定期保存成功');
        }
      } catch (error) {
        console.error('定期保存失败:', error);
      }
    }
  }

  // 触发紧急保存
  private async triggerEmergencySave(reason: string): Promise<void> {
    // 如果正在处理文件且不是录音状态，不进行紧急保存
    if (this.emergencySaveTriggered || 
        this.data.length === 0 || 
        (this.audioFileBeingProcessed !== null && !this.isRecording)) {
      console.log('跳过紧急保存 - 原因:', reason, '已触发:', this.emergencySaveTriggered, '数据长度:', this.data.length, '文件处理且非录音:', this.audioFileBeingProcessed !== null && !this.isRecording);
      return;
    }

    this.emergencySaveTriggered = true;
    console.log(`触发紧急保存，原因: ${reason}，转写条数: ${this.data.length}`);

    try {
      if (this.faultToleranceCallbacks.onEmergencySave) {
        await this.faultToleranceCallbacks.onEmergencySave([...this.data], reason);
      }
    } catch (error) {
      console.error('紧急保存失败:', error);
    }
  }

  // 设置页面卸载监听器
  private setupBeforeUnloadListener(): void {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (this.isRecording && this.data.length > 0) {
        // 触发紧急保存
        this.triggerEmergencySave('页面即将卸载');
        
        // 显示确认对话框
        const message = '录音正在进行中，离开页面将丢失未保存的转写内容。确定要离开吗？';
        event.preventDefault();
        event.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    console.log('已设置页面卸载监听器');
  }

  // 检查并恢复音频处理器状态
  private async checkAndRestoreAudioProcessor(): Promise<void> {
    // 只有在录音状态下才需要检查
    if (!this.isRecording) return;
    
    console.log('检查音频处理器状态...');
    
    // 保存当前录音状态，防止在恢复过程中被重置
    const currentRecordingStartTime = this.recordingStartTime;
    const wasRecording = this.isRecording;
    
    // 如果音频上下文状态不是running，尝试恢复
    if (this.audioContext && this.audioContext.state !== 'running') {
      console.log('音频上下文状态不是running，尝试恢复...');
      try {
        await this.audioContext.resume();
        console.log('音频上下文已恢复');
      } catch (e) {
        console.error('恢复音频上下文失败:', e);
      }
    }
    
    // 检查媒体流的所有轨道是否有效
    if (this.recordingStream) {
      console.log('检查媒体流轨道状态...');
      const tracks = this.recordingStream.getTracks();
      console.log(`媒体流轨道数量: ${tracks.length}`);
      
      // 检查每个轨道的状态
      tracks.forEach((track, index) => {
        console.log(`轨道 ${index} 状态:`, track.readyState, `已启用:`, track.enabled, `ID:`, track.id);
      });
      
      const allTracksActive = tracks.every(track => track.readyState === 'live');
      if (!allTracksActive) {
        console.warn('检测到一个或多个音频轨道不活跃，尝试重新获取媒体流...');
        
        try {
          // 先停止现有的轨道
          this.recordingStream.getTracks().forEach(track => track.stop());
          
          // 重新获取媒体流
          this.recordingStream = await navigator.mediaDevices.getUserMedia({
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true
            }
          });
          
          console.log('成功重新获取媒体流');
          
          // 重新创建音频处理链
          if (this.audioContext) {
            // 断开旧的处理器
            if (this.audioProcessor) {
              this.audioProcessor.disconnect();
              this.audioProcessor = null;
              console.log('已断开旧的音频处理器');
            }
            
            // 创建新的音频源节点
            const sourceNode = this.audioContext.createMediaStreamSource(this.recordingStream);
            console.log('已创建新的音频源节点');
            
            // 创建新的处理器节点
            const processorNode = this.audioContext.createScriptProcessor(4096, 1, 1);
            console.log('已创建新的音频处理器节点');
            
            // 使用createAudioProcessHandler设置处理逻辑
            processorNode.onaudioprocess = this.createAudioProcessHandler();
            
            // 连接节点
            sourceNode.connect(processorNode);
            processorNode.connect(this.audioContext.destination);
            
            // 更新处理器引用
            this.audioProcessor = processorNode;
            
            console.log('音频处理链重建完成');
          } else {
            console.error('音频上下文不存在，无法重建音频处理链');
          }
        } catch (e) {
          console.error('重新获取媒体流失败:', e);
        }
      } else {
        console.log('媒体流轨道状态正常，无需重新获取');
        
        // 即使轨道状态正常，也检查一下AudioProcessor
        if (this.audioProcessor) {
          console.log('音频处理器已存在，无需重建');
        } else {
          console.warn('音频处理器不存在，需要重建');
          
          try {
            // 创建新的音频源节点
            const sourceNode = this.audioContext?.createMediaStreamSource(this.recordingStream);
            if (sourceNode && this.audioContext) {
              console.log('已创建新的音频源节点');
              
              // 创建新的处理器节点
              const processorNode = this.audioContext.createScriptProcessor(4096, 1, 1);
              console.log('已创建新的音频处理器节点');
              
              // 设置处理逻辑
              processorNode.onaudioprocess = (event) => {
                if (!this.isRecording) return;
                
                // 获取音频数据
                const inputData = event.inputBuffer.getChannelData(0);
                
                // 检查音频数据是否有效（是否全部为0或接近0）
                let hasValidAudio = false;
                for (let i = 0; i < Math.min(inputData.length, 100); i++) {
                  if (Math.abs(inputData[i]) > 0.0001) {
                    hasValidAudio = true;
                    break;
                  }
                }
                
                // 如果数据全为0或几乎为0，记录日志并跳过此帧
                if (!hasValidAudio) {
                  console.warn('检测到无效的音频数据（全0或接近0），跳过此帧');
                  return;
                }
                
                // 转换为16位整数
                const pcm16 = new Int16Array(inputData.length);
                for (let i = 0; i < inputData.length; i++) {
                  pcm16[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32768));
                }
                
                // 发送到WebSocket
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                  const uint16 = new Uint16Array(pcm16.length);
                  for (let i = 0; i < pcm16.length; i++) {
                    uint16[i] = pcm16[i];
                  }
                  const pcmBytes = new Uint8Array(uint16.buffer);
                  
                  // 检查数据是否全为0
                  let allZeros = true;
                  // 只检查一部分样本
                  for (let i = 0; i < Math.min(pcmBytes.length, 100); i += 2) {
                    if (pcmBytes[i] !== 0 || pcmBytes[i+1] !== 0) {
                      allZeros = false;
                      break;
                    }
                  }
                  
                  if (allZeros) {
                    console.warn('检测到二进制数据全为0，跳过发送');
                    return;
                  }
                  
                  this.ws.send(pcmBytes);
                }
              };
              
              // 连接节点
              sourceNode.connect(processorNode);
              processorNode.connect(this.audioContext.destination);
              
              // 更新处理器引用
              this.audioProcessor = processorNode;
              
              console.log('音频处理链已重建');
            } else {
              console.error('无法创建音频源节点或音频上下文不存在');
            }
          } catch (e) {
            console.error('重建音频处理链失败:', e);
          }
        }
      }
    } else {
      console.error('媒体流不存在，无法检查轨道状态');
    }
    
    // 确保录音状态和计时器没有被意外重置
    if (wasRecording) {
      this.isRecording = true;
      this.recordingStartTime = currentRecordingStartTime;
      console.log('音频处理器检查完成，录音状态已确保正确');
    }
  }

  // 重采样函数：将音频数据重采样到16kHz
  private resampleTo16kHz(inputData: Float32Array, sourceSampleRate: number): Float32Array {
    if (sourceSampleRate === 16000) {
      return inputData;
    }
    
    const ratio = sourceSampleRate / 16000;
    const outputLength = Math.floor(inputData.length / ratio);
    const outputData = new Float32Array(outputLength);
    
    // 简单的线性插值重采样
    for (let i = 0; i < outputLength; i++) {
      const srcIndex = i * ratio;
      const srcIndexFloor = Math.floor(srcIndex);
      const srcIndexCeil = Math.min(srcIndexFloor + 1, inputData.length - 1);
      const fraction = srcIndex - srcIndexFloor;
      
      outputData[i] = inputData[srcIndexFloor] * (1 - fraction) + inputData[srcIndexCeil] * fraction;
    }
    
    return outputData;
  }

  // 创建音频处理回调函数
  private createAudioProcessHandler(): (event: AudioProcessingEvent) => void {
    return (event: AudioProcessingEvent) => {
      if (!this.isRecording) return;
      
      // 获取音频数据
      const inputData = event.inputBuffer.getChannelData(0);
      
      // 检查是否需要重采样
      const sourceSampleRate = event.inputBuffer.sampleRate;
      let processedData = inputData;
      
      // 如果采样率不是16kHz，进行重采样
      if (sourceSampleRate !== 16000) {
        processedData = this.resampleTo16kHz(inputData, sourceSampleRate);
      }
      
      // 检查音频数据是否有效（是否全部为0或接近0）
      let hasValidAudio = false;
      let nonZeroCount = 0;
      
      // 采样检查前100个样本
      for (let i = 0; i < Math.min(processedData.length, 100); i++) {
        if (Math.abs(processedData[i]) > 0.0001) {
          hasValidAudio = true;
          nonZeroCount++;
        }
      }
      
      // 如果数据全为0或几乎为0
      if (!hasValidAudio) {
        console.warn('检测到无效的音频数据（全0或接近0），连续次数:', ++this.consecutiveZeroFrames);
        
        // 检查是否达到了连续全0帧的阈值且当前未在恢复过程中
        if (this.consecutiveZeroFrames >= this.maxConsecutiveZeroFrames && !this.isRecoveringAudioProcessor) {
          console.warn(`检测到连续${this.consecutiveZeroFrames}帧全0数据，正在尝试恢复音频处理链...`);
          this.triggerAudioProcessorRecovery();
        }
        
        return; // 跳过全0帧处理
      } else {
        // 重置连续全0帧计数
        if (this.consecutiveZeroFrames > 0) {
          console.log(`重置连续全0帧计数，之前有${this.consecutiveZeroFrames}帧全0`);
          this.consecutiveZeroFrames = 0;
        }
      }
      
      // 偶尔记录一下有效数据的比例
      if (Math.random() < 0.01) { // 以1%的概率记录
        // console.log(`音频数据有效性: ${nonZeroCount}/100 样本非零`);
      }
      
      // 转换为16位整数
      const pcm16 = new Int16Array(processedData.length);
      for (let i = 0; i < processedData.length; i++) {
        pcm16[i] = Math.max(-32768, Math.min(32767, processedData[i] * 32768));
      }
      
      // 发送到WebSocket
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        const uint16 = new Uint16Array(pcm16.length);
        for (let i = 0; i < pcm16.length; i++) {
          uint16[i] = pcm16[i];
        }
        const pcmBytes = new Uint8Array(uint16.buffer);
        this.ws.send(pcmBytes);
      }
    };
  }
  
  // 触发音频处理器恢复
  private triggerAudioProcessorRecovery(): Promise<void> {
    if (this.isRecoveringAudioProcessor) {
      return Promise.resolve(); // 已经在恢复中，直接返回解决的Promise
    }
    
    // 标记正在恢复
    this.isRecoveringAudioProcessor = true;
    
    // 保存当前录音状态，确保恢复过程不会影响录音计时
    const wasRecording = this.isRecording;
    const currentRecordingStartTime = this.recordingStartTime;
    
    // 返回Promise
    return new Promise<void>((resolve) => {
      // 使用setTimeout确保不阻塞当前音频处理线程
      setTimeout(async () => {
        try {
          console.log('开始执行音频处理器恢复流程...');
          
          // 断开现有处理器连接，但不停止媒体流
          if (this.audioProcessor) {
            this.audioProcessor.disconnect();
            this.audioProcessor = null;
            console.log('已断开旧的音频处理器');
          }
          
          // 检查并恢复音频上下文
          if (this.audioContext && this.audioContext.state !== 'running') {
            console.log('恢复音频上下文状态...');
            await this.audioContext.resume();
          }
          
          // 检查媒体流
          if (!this.recordingStream || this.recordingStream.getTracks().some(track => track.readyState !== 'live')) {
            console.log('媒体流不可用，尝试重新获取...');
            
            // 停止现有轨道
            if (this.recordingStream) {
              this.recordingStream.getTracks().forEach(track => track.stop());
            }
            
            // 重新获取媒体流
            this.recordingStream = await navigator.mediaDevices.getUserMedia({
              audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
              }
            });
            console.log('成功重新获取媒体流');
          }
          
          // 重新创建音频处理链
          if (this.audioContext && this.recordingStream) {
            // 创建新的音频源节点
            const sourceNode = this.audioContext.createMediaStreamSource(this.recordingStream);
            console.log('已创建新的音频源节点');
            
            // 创建新的处理器节点
            const processorNode = this.audioContext.createScriptProcessor(4096, 1, 1);
            console.log('已创建新的音频处理器节点');
            
            // 使用createAudioProcessHandler，但要确保避免无限递归
            // 如果此时已经检测到多次全0帧，可以先重置计数器再设置处理器
            this.consecutiveZeroFrames = 0; // 重置计数器
            processorNode.onaudioprocess = this.createAudioProcessHandler();
            
            // 连接节点
            sourceNode.connect(processorNode);
            processorNode.connect(this.audioContext.destination);
            
            // 更新处理器引用
            this.audioProcessor = processorNode;
            
            console.log('音频处理链重建完成');
            
            // 重置计数器
            this.consecutiveZeroFrames = 0;
          }
          
          // 恢复录音状态，确保计时器不受影响
          if (wasRecording) {
            this.isRecording = true;
            this.recordingStartTime = currentRecordingStartTime;
            console.log('音频处理器恢复完成，录音状态已恢复');
          }
          
          resolve(); // 恢复成功，解决Promise
        } catch (e) {
          console.error('自动恢复音频处理链失败:', e);
          
          // 即使恢复失败，也要确保录音状态正确
          if (wasRecording) {
            this.isRecording = true;
            this.recordingStartTime = currentRecordingStartTime;
          }
          
          resolve(); // 即使失败也解决Promise，避免阻塞
        } finally {
          // 无论成功与否，重置恢复状态
          this.isRecoveringAudioProcessor = false;
        }
      }, 100);
    });
  }

  // 强制刷新音频处理器
  public async forceRefreshAudioProcessor(): Promise<void> {
    if (!this.isRecording) {
      console.log('当前未在录音状态，不需要刷新音频处理器');
      return;
    }
    
    console.log('强制刷新音频处理器...');
    this.consecutiveZeroFrames = 0;
    
    // 直接触发恢复流程
    if (!this.isRecoveringAudioProcessor) {
      await this.triggerAudioProcessorRecovery();
      console.log('音频处理器刷新完成');
    } else {
      console.log('已有音频处理器恢复流程正在进行，跳过重复刷新');
    }
  }

  private reportDecodeProgress(status: string, progress?: number) {
    if (this.onDecodeProgress) {
      this.onDecodeProgress(status, progress);
    }
  }
}

export default AudioTranscriber;