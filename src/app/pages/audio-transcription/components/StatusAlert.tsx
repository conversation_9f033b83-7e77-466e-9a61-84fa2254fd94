'use client'
import React from 'react';
import { Alert, Spin } from 'antd';

interface StatusAlertProps {
  processingStatus: {
    message: string;
    type: 'processing' | 'success' | 'error';
  } | null;
  isRecording: boolean;
  isProcessing: boolean;
  recordTime: string;
}

const StatusAlert: React.FC<StatusAlertProps> = ({
  processingStatus,
  isRecording,
  isProcessing,
  recordTime
}) => {
  if (!processingStatus && !isRecording) return null;
  
  return (
    <div style={{ 
      position: 'fixed', 
      top: '80px', 
      right: '20px',
      zIndex: 100,
      width: '400px',
      pointerEvents: 'none'
    }}>
      <Alert
        message={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {processingStatus ? processingStatus.message : '正在录音中...'}
            </span>
            {isRecording && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: '16px', fontWeight: 'bold' }}>{recordTime}</span>
              </div>
            )}
          </div>
        }
        type={(processingStatus && processingStatus.type !== 'processing') ? processingStatus.type : 'info'}
        showIcon
        style={{ 
          marginBottom: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          pointerEvents: 'auto',
          width: '100%'
        }}
        action={
          isProcessing && !isRecording && (
            <div style={{ marginLeft: '16px' }}>
              <Spin size="small" />
            </div>
          )
        }
      />
    </div>
  );
};

export default StatusAlert; 