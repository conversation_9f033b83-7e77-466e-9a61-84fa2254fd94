'use client'
import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Button, Alert } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import Marquee from 'react-fast-marquee';

const { Title } = Typography;

// 定义主题色
const primaryColor = 'rgba(255, 206, 57, 1)';
const primaryColorLight = 'rgba(255, 206, 57, 0.1)';

interface PageHeaderProps {
  onBackClick: () => void;
  onMarqueeClick?: () => void;
}

const PageHeader: React.FC<PageHeaderProps> = ({ onBackClick, onMarqueeClick }) => {
  const [userType, setUserType] = useState<number | null>(null);
  
  // 获取用户类型
  useEffect(() => {
    const storedUserType = localStorage.getItem('user_type');
    if (storedUserType) {
      setUserType(parseInt(storedUserType, 10));
    }
  }, []);
  
  // 处理返回按钮点击
  const handleBackClick = () => {
    // 如果是用户类型3，不执行返回操作
    if (userType === 3) {
      console.log('用户类型3，禁止返回');
      return;
    }
    onBackClick();
  };

  return (
    <div style={{ 
      height: '64px', 
      padding: '0 24px', 
      background: '#fff', 
      borderBottom: '1px solid #f0f0f0',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      boxShadow: '0 1px 4px rgba(0, 0, 0, 0.05)'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {/* 用户类型3时隐藏返回按钮 */}
        {userType !== 3 && (
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleBackClick}
            style={{ 
              marginRight: '16px',
              color: '#333'
            }}
            type="text"
          />
        )}
        <Title level={4} style={{ margin: 0 }}>
          模型体验：音频转写
        </Title>
      </div>
      
      {/* 轮播广告 */}
      <div style={{ flex: 1, maxWidth: '500px', marginLeft: '24px' }}>
        <Alert
          banner
          type="info"
          style={{ 
            backgroundColor: '#e6f7ff',
            border: '1px solid #91d5ff',
            borderRadius: '6px',
            cursor: 'pointer'
          }}
          onClick={onMarqueeClick}
          message={
            <Marquee pauseOnHover gradient={false} speed={50}>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                欢迎体验电牙Demo系统！当前产品处于测试阶段，部分功能仍在优化中
              </span>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                设备兼容性：暂未适配手机端，请使用PC网页登录
              </span>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                实时录音转写：录音开始后的前20秒为沙箱状态，可用于会议准备
              </span>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                查看会议总结：总结完成后，请点击「历史记录」顶部的第一条记录查看总结内容
              </span>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                转写进度条卡住：若进度条超过5分钟无变化，请重新上传文件
              </span>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                其他转写异常：上传后卡在准备页面？重试多次后问题依旧（联系客服处理）
              </span>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                可能出现部分内容丢失（正在修复，请重新尝试）
              </span>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                录音中时长突变为"00:00"（请忽略）
              </span>
              <span style={{ marginRight: '80px', color: '#1890ff', fontSize: '13px' }}>
                文件上传限制：测试文件仅支持MP3/WAV/M4A格式，且文件大小≤200MB
              </span>
            </Marquee>
          }
        />
      </div>
    </div>
  );
};

export default PageHeader; 