'use client'
import React from 'react';
import { Button, Typography, message, Spin, Popconfirm } from 'antd';
import { 
  AudioOutlined, 
  PauseOutlined, 
  ThunderboltOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { TranscriptMessage } from '../AudioTranscriber';

const { Title, Text, Paragraph } = Typography;

// 定义主题色
const primaryColor = 'rgba(255, 206, 57, 1)';
const primaryColorLight = 'rgba(255, 206, 57, 0.1)';

interface RecordingPanelProps {
  isRecording: boolean;
  isProcessing: boolean;
  recordTime: string;
  currentMessages: TranscriptMessage[];
  onStartRecording: () => void;
  onStopRecording: () => void;
  onGenerateSummary?: () => void;
  isGeneratingSummary?: boolean;
  onExportPDF?: () => void;
  isHistoryRecord?: boolean;
  recordId?: number;
  onDeleteRecord?: (id: number) => void;
}

const RecordingPanel: React.FC<RecordingPanelProps> = ({
  isRecording,
  isProcessing,
  recordTime,
  currentMessages,
  onStartRecording,
  onStopRecording,
  onGenerateSummary,
  isGeneratingSummary = false,
  onExportPDF,
  isHistoryRecord = false,
  recordId,
  onDeleteRecord
}) => {
  const hasTranscript = currentMessages && currentMessages.length > 0;
  
  // 未开始录音时的界面
  if (!isRecording && !hasTranscript) {
    return (
      <div style={{ maxWidth: '100%', width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* 标题区域 */}
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>实时录音转写</Title>
            <Text type="secondary">支持中文、英文等多种语言的实时录音转写，实时显示转写结果</Text>
          </div>
          
          {/* 如果是历史记录，显示删除按钮 */}
          {isHistoryRecord && recordId && onDeleteRecord && (
            <Popconfirm
              title="确认删除"
              description="确定要删除这条转写记录吗？删除后无法恢复。"
              onConfirm={() => onDeleteRecord(recordId)}
              okText="删除"
              cancelText="取消"
              okType="danger"
            >
              <Button 
                danger
                icon={<DeleteOutlined />}
                size="middle"
              >
                删除记录
              </Button>
            </Popconfirm>
          )}
        </div>
        
        {/* 开始录音按钮区域 */}
        <div 
          style={{ 
            background: '#fafafa', 
            borderRadius: '8px',
            border: '1px dashed #d9d9d9',
            padding: '40px 20px', 
            textAlign: 'center',
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            maxHeight: '320px'
          }}
        >
          <AudioOutlined style={{ fontSize: '36px', color: primaryColor, marginBottom: '12px' }} />
          <div>
            <Text>点击下方按钮开始录音</Text>
          </div>
          <div style={{ marginTop: '12px' }}>
            <Button
              type="primary"
              icon={<AudioOutlined />}
              onClick={onStartRecording}
              disabled={isProcessing && !isRecording}
              style={{ width: '160px' }}
            >
              开始录音
            </Button>
          </div>
          <div style={{ marginTop: '16px', color: '#666' }}>
            <Text>· 实时录音转写功能时长限制为100小时</Text>
          </div>
        </div>
      </div>
    );
  }
  
  // 开始录音后的界面
  return (
    <div style={{ maxWidth: '100%', width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 标题区域 */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={4} style={{ margin: 0 }}>实时录音转写</Title>
          <Text type="secondary">支持中文、英文等多种语言的实时录音转写，实时显示转写结果</Text>
        </div>
        
        {/* 如果是历史记录，显示删除按钮 */}
        {isHistoryRecord && recordId && onDeleteRecord && (
          <Popconfirm
            title="确认删除"
            description="确定要删除这条转写记录吗？删除后无法恢复。"
            onConfirm={() => onDeleteRecord(recordId)}
            okText="删除"
            cancelText="取消"
            okType="danger"
          >
            <Button 
              danger
              icon={<DeleteOutlined />}
              size="middle"
            >
              删除记录
            </Button>
          </Popconfirm>
        )}
      </div>

      {/* 转写内容展示区域 */}
      <div 
        style={{ 
          background: hasTranscript ? '#fff' : '#fafafa', 
          border: '1px solid #f0f0f0',
          borderRadius: '8px',
          padding: '16px',
          flex: 1,
          overflowY: 'auto',
          marginBottom: '16px',
          minHeight: '320px'
        }}
        className="transcript-container"
      >
        {hasTranscript ? (
          <div>
            {(() => {
              const filteredMessages = currentMessages
              .filter((message, index, self) => 
                index === self.findIndex(m => m.id === message.id)
                );
              
              return filteredMessages.map((message, index) => (
                <Paragraph
                  key={`message_${message.id}_${index}`}
                  style={{ 
                    marginBottom: '16px',
                    fontSize: '15px',
                    lineHeight: '1.6'
                  }}
                >
                  {message.content}
                </Paragraph>
              ));
            })()}
          </div>
        ) : (
          <div style={{ 
            height: '100%', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            color: '#999',
            flexDirection: 'column'
          }}>
            {isRecording ? (
              <>
                <Spin size="large" />
                <div style={{ marginTop: '16px' }}>
                  <Text type="secondary">前20秒为模型转写沙箱时间，请正常说话，稍后将显示转写结果...</Text>
                </div>
              </>
            ) : (
              <Text type="secondary">准备就绪，等待转写内容...</Text>
            )}
          </div>
        )}
      </div>
      
      {/* 底部操作区域 - 录音时的计时和按钮 */}
      <div style={{ marginBottom: '16px' }}>
        {isRecording ? (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'center' 
          }}>
            {/* 左侧：录音计时 */}
            <div style={{
              display: 'flex',
              alignItems: 'center'
            }}>
              <div style={{ 
                width: '10px', 
                height: '10px', 
                borderRadius: '50%', 
                background: '#f5222d', 
                marginRight: '12px',
                animation: 'pulse 1.5s infinite'
              }} />
              <Text style={{ 
                fontSize: '24px', 
                fontWeight: 'bold',
                color: '#333'
              }}>{recordTime}</Text>
            </div>
            
            {/* 右侧：停止录音按钮 */}
            <Button
              danger
              icon={<PauseOutlined />}
              onClick={onStopRecording}
              size="middle"
            >
              停止录音
            </Button>
          </div>
        ) : (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'center' 
          }}>
            {/* 左侧：按钮 */}
            <Button
              type="primary"
              icon={<AudioOutlined />}
              onClick={onStartRecording}
              disabled={isProcessing && !isRecording}
            >
              开始新录音
            </Button>
            
            {/* 右侧：按钮组 */}
            <div>
              {hasTranscript && onExportPDF && (
                <Button 
                  icon={<FilePdfOutlined />} 
                  onClick={onExportPDF}
                  style={{ marginRight: '8px' }}
                >
                  导出PDF
                </Button>
              )}
              <Button 
                icon={<FileTextOutlined />} 
                onClick={onGenerateSummary}
                loading={isGeneratingSummary}
              >
                录音转总结
              </Button>
            </div>
          </div>
        )}
      </div>
      
      {/* 添加录音时间动画效果 */}
      <style jsx global>{`
        @keyframes pulse {
          0% {
            opacity: 1;
          }
          50% {
            opacity: 0.3;
          }
          100% {
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

export default RecordingPanel; 