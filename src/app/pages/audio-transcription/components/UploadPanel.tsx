'use client'
import React, { useState } from 'react';
import { But<PERSON>, Typography, Upload, Progress, Spin, message, Tabs, Popconfirm } from 'antd';
import ReactMarkdown from 'react-markdown';
import { 
  UploadOutlined, 
  FileOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  FilePdfOutlined,
  SyncOutlined,
  FileTextOutlined,
  BulbOutlined,
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { TranscriptMessage } from '../AudioTranscriber';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

// 定义主题色
const primaryColor = 'rgba(255, 206, 57, 1)';
const primaryColorLight = 'rgba(255, 206, 57, 0.1)';
const primaryColorMedium = 'rgba(255, 206, 57, 0.8)';

// 定义音频文件的魔数（Magic Numbers）用于文件头验证
const AUDIO_MAGIC_NUMBERS = {
  'mp3': [0x49, 0x44, 0x33], // ID3
  'mp3_alt': [0xFF, 0xFB], // MPEG-1 Layer 3
  'mp3_alt2': [0xFF, 0xF3], // MPEG-1 Layer 3
  'mp3_alt3': [0xFF, 0xF2], // MPEG-1 Layer 3
  'wav': [0x52, 0x49, 0x46, 0x46], // RIFF
  'aac': [0xFF, 0xF1], // AAC ADTS
  'aac_alt': [0xFF, 0xF9], // AAC ADTS
  'm4a': [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70], // ftyp
  'm4a_alt': [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70], // ftyp
  'ogg': [0x4F, 0x67, 0x67, 0x53], // OggS
  'flac': [0x66, 0x4C, 0x61, 0x43], // fLaC
  'wma': [0x30, 0x26, 0xB2, 0x75, 0x8E, 0x66, 0xCF, 0x11], // WMA
  'amr': [0x23, 0x21, 0x41, 0x4D, 0x52], // #!AMR
  'mp4': [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70], // ftyp (mp4)
  'mp4_alt': [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70] // ftyp (mp4)
};

// 定义严格的文件扩展名白名单
const ALLOWED_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.mp4', '.wma', '.ogg', '.amr', '.flac'];

// 定义支持的音频格式
const SUPPORTED_AUDIO_FORMATS = ['.mp3', '.wav', '.m4a', '.aac', '.mp4', '.wma', '.ogg', '.amr', '.flac'];
const SUPPORTED_MIME_TYPES = [
  'audio/mpeg', 
  'audio/wav', 
  'audio/mp4', 
  'audio/x-m4a',
  'audio/aac',
  'video/mp4',
  'audio/x-ms-wma',
  'audio/ogg',
  'video/ogg',
  'audio/amr',
  'audio/flac'
];

// 显示模式类型
type DisplayMode = 'transcript' | 'summary' | 'overview';

interface UploadPanelProps {
  isProcessing: boolean;
  fileList: UploadFile[];
  currentMessages: TranscriptMessage[];
  onFileUpload: UploadProps['onChange'];
  processingStatus?: string;
  processingProgress?: number;
  decodeStatus?: string;
  decodeProgress?: number;
  onExportPDF?: () => void;
  onExportTranscriptPDF?: () => void;
  onExportSummaryPDF?: (summaryContent: string) => void;
  onExportOverviewPDF?: (overviewContent: string) => void;
  onViewSummary?: () => void;
  onViewOverview?: () => void;
  hasSummary?: boolean;
  hasOverview?: boolean;
  summaryContent?: string;
  overviewContent?: string;
  isHistoryRecord?: boolean;
  recordId?: number;
  onDeleteRecord?: (id: number) => void;
  taskStatus?: string;
  messageApi?: any;
}

const UploadPanel: React.FC<UploadPanelProps> = ({
  isProcessing,
  fileList,
  currentMessages,
  onFileUpload,
  processingStatus = '正在处理...',
  processingProgress = 0,
  decodeStatus,
  decodeProgress,
  onExportPDF,
  onExportTranscriptPDF,
  onExportSummaryPDF,
  onExportOverviewPDF,
  onViewSummary,
  onViewOverview,
  hasSummary = false,
  hasOverview = false,
  summaryContent = '',
  overviewContent = '',
  isHistoryRecord = false,
  recordId,
  onDeleteRecord,
  taskStatus,
  messageApi
}) => {
  const [activeTab, setActiveTab] = useState<string>('transcript');
  const [uploadError, setUploadError] = useState<string | null>(null);
  
  const hasFile = fileList && fileList.length > 0;
  const hasTranscriptContent = currentMessages && currentMessages.length > 0;
  const currentFile = hasFile ? fileList[0] : null;
  
  // 获取任务状态显示信息
  const getTaskStatusInfo = (status?: string) => {
    if (!status) return null;

    switch (status) {
      case 'done':
        return {
          color: '#52c41a',
          text: '转写完成',
          icon: <CheckCircleOutlined />
        };
      case 'before_callback':
        return {
          color: '#1890ff',
          text: '准备中',
          icon: <LoadingOutlined />
        };
      case 'convert_running':
        return {
          color: '#1890ff',
          text: '音频转换中',
          icon: <LoadingOutlined />
        };
      case 'transcribe_pending':
        return {
          color: '#faad14',
          text: '转写等待中',
          icon: <ClockCircleOutlined />
        };
      case 'transcribe_running':
        return {
          color: '#1890ff',
          text: '转写中',
          icon: <LoadingOutlined />
        };
      case 'summarize_pending':
        return {
          color: '#faad14',
          text: '总结等待中',
          icon: <ClockCircleOutlined />
        };
      case 'summarize_running':
        return {
          color: '#1890ff',
          text: '总结生成中',
          icon: <LoadingOutlined />
        };
      case 'convert_failed':
        return {
          color: '#ff4d4f',
          text: '转写失败',
          icon: <ExclamationCircleOutlined />
        };
      case 'transcribe_failed':
        return {
          color: '#ff4d4f',
          text: '转写失败',
          icon: <ExclamationCircleOutlined />
        };
      case 'summarize_failed':
        return {
          color: '#ff4d4f',
          text: '总结生成失败',
          icon: <ExclamationCircleOutlined />
        };
      case 'failed':
        return {
          color: '#ff4d4f',
          text: '处理失败',
          icon: <ExclamationCircleOutlined />
        };
      default:
        return {
          color: '#8c8c8c',
          text: status,
          icon: <ClockCircleOutlined />
        };
    }
  };
  
  // 获取文件大小的格式化显示
  const getFormattedSize = (size?: number) => {
    if (!size) return '未知大小';
    
    if (size > 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    } else if (size > 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    } else {
      return `${size} B`;
    }
  };
  
  // 安全的文件名验证函数
  const validateFileName = (fileName: string): { isValid: boolean; error?: string } => {
    if (!fileName || typeof fileName !== 'string') {
      return { isValid: false, error: '文件名无效' };
    }
    
    // 检查文件名长度
    if (fileName.length > 255) {
      return { isValid: false, error: '文件名过长' };
    }
    
    // 检查是否包含危险字符
    const dangerousChars = /[<>:"|?*\x00-\x1F]/;
    if (dangerousChars.test(fileName)) {
      return { isValid: false, error: '文件名包含非法字符' };
    }
    
    // 检查是否以点开头（隐藏文件）
    if (fileName.startsWith('.')) {
      return { isValid: false, error: '不允许上传隐藏文件' };
    }
    
    // 获取所有扩展名（处理双扩展名）
    const parts = fileName.toLowerCase().split('.');
    if (parts.length < 2) {
      return { isValid: false, error: '文件必须有扩展名' };
    }
    
    // 检查是否存在多个扩展名（双扩展名攻击）
    if (parts.length > 2) {
      // 检查是否有可执行文件扩展名
      const executableExtensions = [
        'exe', 'bat', 'cmd', 'com', 'scr', 'pif', 'vbs', 'js', 'jar', 
        'msi', 'ps1', 'sh', 'py', 'php', 'asp', 'aspx', 'jsp'
      ];
      
      for (let i = 1; i < parts.length - 1; i++) {
        if (executableExtensions.includes(parts[i])) {
          return { isValid: false, error: '检测到可疑的文件扩展名' };
        }
      }
    }
    
    // 验证最终扩展名
    const finalExtension = '.' + parts[parts.length - 1];
    if (!ALLOWED_EXTENSIONS.includes(finalExtension)) {
      return { isValid: false, error: `不支持的文件格式: ${finalExtension}` };
    }
    
    return { isValid: true };
  };

  // 文件头验证函数
  const validateFileHeader = async (file: File): Promise<{ isValid: boolean; error?: string }> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      
      reader.onload = function(e) {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          if (!arrayBuffer) {
            resolve({ isValid: false, error: '无法读取文件内容' });
            return;
          }
          
          const bytes = new Uint8Array(arrayBuffer);
          let isValidHeader = false;
          
          // 检查各种音频文件的魔数
          for (const [format, magicNumbers] of Object.entries(AUDIO_MAGIC_NUMBERS)) {
            if (bytes.length >= magicNumbers.length) {
              const matches = magicNumbers.every((byte, index) => bytes[index] === byte);
              if (matches) {
                isValidHeader = true;
                break;
              }
            }
          }
          
          if (!isValidHeader) {
            resolve({ isValid: false, error: '文件头验证失败，可能不是有效的音频文件' });
            return;
          }
          
          resolve({ isValid: true });
        } catch (error) {
          resolve({ isValid: false, error: '文件头验证过程中发生错误' });
        }
      };
      
      reader.onerror = function() {
        resolve({ isValid: false, error: '无法读取文件' });
      };
      
      // 只读取文件的前64字节用于头部验证
      const blob = file.slice(0, 64);
      reader.readAsArrayBuffer(blob);
    });
  };

  // 通过解析MP4文件结构检测是否包含视频轨道
  const checkMP4VideoTrack = async (file: File): Promise<{ hasVideo: boolean; error?: string }> => {
    return new Promise((resolve) => {
      // 获取文件扩展名
      const extension = file.name.toLowerCase().split('.').pop();
      if (extension !== 'mp4') {
        resolve({ hasVideo: false });
        return;
      }

      console.log('开始检测MP4文件视频轨道:', file.name, '文件类型:', file.type);

      // 首先检查MIME类型，如果明确是audio/mp4，则允许
      if (file.type === 'audio/mp4' || file.type === 'audio/x-mp4') {
        console.log('根据MIME类型判断为音频文件，允许上传');
        resolve({ hasVideo: false });
        return;
      }

      // 读取文件头部信息来检测MP4结构
      const reader = new FileReader();
      
      reader.onload = function(e) {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          if (!arrayBuffer) {
            console.log('无法读取文件内容，默认允许上传');
            resolve({ hasVideo: false });
            return;
          }
          
          const bytes = new Uint8Array(arrayBuffer);
          let hasVideoTrack = false;
          
          // 搜索ftyp box中的brand信息
          let offset = 0;
          while (offset < bytes.length - 8) {
            // 读取box大小
            const boxSize = (bytes[offset] << 24) | (bytes[offset + 1] << 16) | 
                           (bytes[offset + 2] << 8) | bytes[offset + 3];
            
            // 读取box type
            const boxType = String.fromCharCode(bytes[offset + 4], bytes[offset + 5], 
                                              bytes[offset + 6], bytes[offset + 7]);
            
            console.log(`发现MP4 box: ${boxType}, 大小: ${boxSize}`);
            
            if (boxType === 'ftyp') {
              // 检查brand信息
              const brand = String.fromCharCode(bytes[offset + 8], bytes[offset + 9], 
                                              bytes[offset + 10], bytes[offset + 11]);
              console.log(`MP4 brand: ${brand}`);
              
              // 检查是否为音频专用格式
              if (brand === 'M4A ' || brand === 'M4B ' || brand === 'M4P ') {
                console.log('检测到音频专用MP4格式，允许上传');
                resolve({ hasVideo: false });
                return;
              }
            } else if (boxType === 'moov' || boxType === 'trak') {
              // 在moov或trak box中搜索视频轨道信息
              const boxData = bytes.slice(offset, offset + Math.min(boxSize, bytes.length - offset));
              const boxStr = String.fromCharCode.apply(null, Array.from(boxData));
              
              // 搜索视频相关的标识符
              if (boxStr.includes('vide') || boxStr.includes('avc1') || boxStr.includes('mp4v') || 
                  boxStr.includes('h264') || boxStr.includes('hvc1') || boxStr.includes('hev1')) {
                console.log('在MP4文件中发现视频轨道标识符');
                hasVideoTrack = true;
                break;
              }
            }
            
            // 移动到下一个box
            if (boxSize <= 0 || boxSize > bytes.length - offset) {
              break;
            }
            offset += boxSize;
          }
          
          if (hasVideoTrack) {
            console.warn('通过文件结构检测到视频轨道，拒绝上传');
            resolve({ hasVideo: true, error: '不支持视频文件，请上传纯音频格式的MP4文件' });
          } else {
            console.log('未检测到视频轨道，使用video元素进行二次验证');
            // 如果文件结构检测没有发现视频轨道，使用video元素进行二次验证
            verifyWithVideoElement(file, resolve);
          }
        } catch (error) {
          console.error('MP4文件结构解析失败:', error);
          // 如果解析失败，使用video元素检测
          verifyWithVideoElement(file, resolve);
        }
      };
      
      reader.onerror = function() {
        console.error('读取MP4文件失败，使用video元素检测');
        verifyWithVideoElement(file, resolve);
      };
      
      // 读取文件前64KB用于结构分析
      const blob = file.slice(0, 64 * 1024);
      reader.readAsArrayBuffer(blob);
    });
  };

  // 使用video元素进行验证的辅助函数
  const verifyWithVideoElement = (file: File, resolve: (value: { hasVideo: boolean; error?: string }) => void) => {
    const video = document.createElement('video');
    const url = URL.createObjectURL(file);
    
    video.muted = true;
    video.preload = 'metadata';
    
    const timeout = setTimeout(() => {
      console.log('Video元素检测超时，默认允许上传');
      URL.revokeObjectURL(url);
      video.remove();
      resolve({ hasVideo: false });
    }, 5000);
    
    video.onloadedmetadata = () => {
      clearTimeout(timeout);
      
      // 检查是否真的有视频内容
      let hasVideo = false;
      
      if (video.videoWidth > 0 && video.videoHeight > 0) {
        // 尝试绘制一帧到canvas来验证是否有实际视频内容
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          
          // 尝试绘制视频帧
          ctx?.drawImage(video, 0, 0);
          
          // 检查画布是否有内容
          if (ctx) {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            
            // 计算非透明且非黑色像素的数量和多样性
            let nonBlackPixelCount = 0;
            let colorVariations = new Set();
            
            for (let i = 0; i < data.length; i += 4) {
              const r = data[i];
              const g = data[i + 1];
              const b = data[i + 2];
              const a = data[i + 3];
              
              // 检查非透明且非纯黑的像素
              if (a > 0 && (r > 10 || g > 10 || b > 10)) {
                nonBlackPixelCount++;
                // 记录颜色变化，用于检测是否有真实视频内容
                const colorKey = `${Math.floor(r/32)}-${Math.floor(g/32)}-${Math.floor(b/32)}`;
                colorVariations.add(colorKey);
              }
            }
            
            // 如果有足够的像素变化和颜色多样性，才认为是视频
            const pixelThreshold = Math.max(100, canvas.width * canvas.height * 0.01); // 至少1%的像素有内容
            const colorThreshold = 3; // 至少3种不同的颜色组合
            
            hasVideo = nonBlackPixelCount > pixelThreshold && colorVariations.size >= colorThreshold;
            
            console.log('Canvas检测详情:', {
              非黑像素数量: nonBlackPixelCount,
              像素阈值: pixelThreshold,
              颜色变化数: colorVariations.size,
              颜色阈值: colorThreshold,
              判断结果: hasVideo
            });
          }
        } catch (error) {
          // 如果绘制失败，可能是纯音频文件
          console.log('无法绘制视频帧，可能是纯音频文件:', error);
          hasVideo = false;
        }
      }
      
      console.log('Video元素检测结果:', {
        文件名: file.name,
        视频宽度: video.videoWidth,
        视频高度: video.videoHeight,
        时长: video.duration,
        是否包含视频: hasVideo
      });
      
      URL.revokeObjectURL(url);
      video.remove();
      
      if (hasVideo) {
        console.warn('Video元素检测到视频轨道，拒绝上传');
        resolve({ hasVideo: true, error: '不支持视频文件，请上传纯音频格式的MP4文件' });
      } else {
        console.log('Video元素检测为音频文件，允许上传');
        resolve({ hasVideo: false });
      }
    };
    
    video.onerror = (e) => {
      clearTimeout(timeout);
      console.log('Video元素加载失败，可能是纯音频文件:', e);
      URL.revokeObjectURL(url);
      video.remove();
      resolve({ hasVideo: false });
    };
    
    video.src = url;
    video.load();
  };

  // 综合文件验证函数
  const validateFileSecurely = async (file: File): Promise<{ isValid: boolean; error?: string }> => {
    // 1. 基本文件对象验证
    if (!file || !(file instanceof File)) {
      return { isValid: false, error: '无效的文件对象' };
    }
    
    // 2. 文件大小验证
    const maxSize = 200 * 1024 * 1024; // 200MB
    if (file.size > maxSize) {
      return { isValid: false, error: '文件大小不能超过200MB' };
    }
    
    if (file.size === 0) {
      return { isValid: false, error: '文件大小为0，可能是空文件' };
    }
    
    // 3. 文件名验证
    const nameValidation = validateFileName(file.name);
    if (!nameValidation.isValid) {
      return nameValidation;
    }
    
    // 4. MP4视频轨道检测（在文件头验证之前执行）
    const mp4Check = await checkMP4VideoTrack(file);
    if (mp4Check.hasVideo) {
      return { isValid: false, error: mp4Check.error };
    }
    
    // 5. 文件头验证
    const headerValidation = await validateFileHeader(file);
    if (!headerValidation.isValid) {
      return headerValidation;
    }
    
    return { isValid: true };
  };

  // 验证文件格式
  const validateFileFormat = async (file: File): Promise<boolean> => {
    const validation = await validateFileSecurely(file);
    console.log('文件验证结果:', validation);
    if (!validation.isValid) {
      console.log('文件验证失败，显示错误消息:', validation.error);
      
      // 设置错误状态以在UI中显示
      setUploadError(validation.error || '文件验证失败');
      
      // 显示更明显的错误提示
      console.log('即将显示错误提示:', validation.error);
      const msgApi = messageApi || message; // 优先使用传入的messageApi
      msgApi.error({
        content: `${validation.error || '文件验证失败'}`,
        duration: 8, // 显示8秒
        style: {
          marginTop: '20vh', // 显示在页面中上方
          fontSize: '16px',
          fontWeight: 'bold',
          zIndex: 10000 // 确保在最上层显示
        }
      });
      console.log('错误提示已调用');
      
      // 3秒后清除错误状态
      setTimeout(() => {
        setUploadError(null);
      }, 3000);
      
      return false;
    }
    console.log('文件验证通过');
    return true;
  };

  // 处理文件上传前的验证
  const handleBeforeUpload = (file: File) => {
    console.log('beforeUpload 被调用, 文件:', file.name, '类型:', file.type);
    
    // 对于异步验证，我们需要返回一个Promise
    return new Promise<boolean>(async (resolve, reject) => {
      try {
        // 使用新的安全验证函数
        const isValid = await validateFileFormat(file);
        console.log('validateFileFormat 返回结果:', isValid);
        if (!isValid) {
          console.log('验证失败，阻止上传');
          // 使用reject来彻底阻止文件处理
          reject(new Error('文件验证失败'));
          return;
        }
        
        console.log('文件验证通过:', file.name);
        // 直接调用onFileUpload来处理文件
        if (onFileUpload) {
          // 创建一个模拟的upload info对象
          const uploadInfo = {
            file: {
              ...file,
              status: 'done' as const,
              uid: Date.now().toString(),
              originFileObj: file as any
            },
            fileList: []
          };
          
          setTimeout(() => {
            onFileUpload(uploadInfo);
          }, 0);
        }
        
        resolve(false); // 阻止自动上传，手动处理
      } catch (error) {
        console.error('文件验证过程中发生错误:', error);
        const msgApi = messageApi || message; // 优先使用传入的messageApi
        msgApi.error('文件验证失败，请重试');
        reject(error);
      }
    });
  };

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    // 调用相应的回调函数
    if (key === 'summary' && onViewSummary) {
      onViewSummary();
    } else if (key === 'overview' && onViewOverview) {
      onViewOverview();
    }
  };

  // 渲染转写内容
  const renderTranscriptContent = () => {
    if (hasTranscriptContent) {
      const filteredMessages = currentMessages
        .filter((message, index, self) => 
          index === self.findIndex(m => m.id === message.id)
        );
      
      console.log('UploadPanel消息渲染:', {
        原始消息数: currentMessages.length,
        去重后消息数: filteredMessages.length,
        消息ID列表: filteredMessages.map(m => m.id),
        消息内容预览: filteredMessages.map(m => m.content?.substring(0, 20) + '...')
      });
      
      return (
        <div>
          {filteredMessages.map((message, index) => {
              // 获取对话人标识
              const speakerLabel = message.speaker !== undefined 
                ? `说话人${message.speaker + 1}：` 
                : '说话人：';
              
              return (
                <div
                  key={`message_${message.id}_${index}`}
                  style={{ 
                    marginBottom: '16px'
                  }}
                >
                  <Paragraph
                    style={{ 
                      marginBottom: 0,
                      fontSize: '15px',
                      lineHeight: '1.6'
                    }}
                  >
                    <span style={{ 
                      fontWeight: 'bold', 
                      color: '#666',
                      marginRight: '8px'
                    }}>
                      {speakerLabel}
                    </span>
                    {message.content}
                  </Paragraph>
                </div>
              );
            })
          }
        </div>
      );
    } else {
      // 检查是否有失败状态
      const statusInfo = getTaskStatusInfo(taskStatus);
      const isFailedStatus = statusInfo && statusInfo.color === '#ff4d4f';
      
      if (isFailedStatus) {
        return (
          <div style={{ 
            height: '200px', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            color: '#999',
            flexDirection: 'column'
          }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              flexDirection: 'column',
              color: statusInfo.color
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                {statusInfo.icon}
              </div>
              <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '8px' }}>
                {statusInfo.text}
              </div>
              <div style={{ fontSize: '14px', color: '#666', textAlign: 'center' }}>
                {taskStatus === 'convert_failed' && '音频格式不支持或文件损坏，请尝试重新上传'}
                {taskStatus === 'transcribe_failed' && '转写过程中出现错误，请重新上传文件'}
                {taskStatus === 'summarize_failed' && '总结生成失败，但转写内容可能已完成'}
                {taskStatus === 'failed' && '处理过程中出现错误，请重新上传文件'}
              </div>
            </div>
          </div>
        );
      }
      
      return (
        <div style={{ 
          height: '200px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          color: '#999',
          flexDirection: 'column'
        }}>
          {isProcessing ? (
            <>
              <Spin size="large" />
              <div style={{ marginTop: '16px' }}>
                {/* 根据不同状态显示不同提示文本 */}
                {decodeStatus ? (
                  <Text type="secondary">文件上传中，请勿关闭浏览器或者切换界面</Text>
                ) : processingStatus?.includes('正在上传文件') ? (
                  <Text type="secondary">文件上传中，请勿关闭浏览器或者切换界面</Text>
                ) : (
                  <Text type="secondary">正在转写中，请稍等...</Text>
                )}
              </div>
            </>
          ) : (
            <Text type="secondary">文件已上传，正在准备转写...</Text>
          )}
        </div>
      );
    }
  };

  // 渲染Markdown内容的通用组件
  const renderMarkdownContent = (content: string, emptyIcon: React.ReactNode, emptyText: string) => {
    if (content) {
      return (
        <div style={{ 
          lineHeight: '1.8', 
          fontSize: '14px',
          color: '#333'
        }}>
          <ReactMarkdown>{content}</ReactMarkdown>
        </div>
      );
    } else {
      return (
        <div style={{ textAlign: 'center', color: '#999', padding: '40px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>
            {emptyIcon}
          </div>
          <div>{emptyText}</div>
        </div>
      );
    }
  };
  
  // 文件上传前的界面
  if (!hasFile) {
    return (
      <div style={{ maxWidth: '100%', width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* 标题区域 */}
        <div style={{ marginBottom: '16px', textAlign: 'left', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={4} style={{ margin: 0 }}>上传文件转写</Title>
            <Text type="secondary">支持mp3、wav、m4a、aac、mp4、wma、ogg、amr、flac等音频格式，文件大小不超过200MB</Text>
          </div>
        </div>
        
        {/* 上传区域 */}
        <div 
          style={{ 
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            background: '#fafafa', 
            borderRadius: '8px',
            border: '1px dashed #d9d9d9',
            padding: '40px 20px',
            height: '320px'
          }}
        >
          <Upload
            accept=".mp3,.wav,.m4a,.aac,.mp4,.wma,.ogg,.amr,.flac"
            maxCount={1}
            multiple={false}
            fileList={fileList}
            onChange={onFileUpload}
            beforeUpload={handleBeforeUpload}
            showUploadList={false}
          >
            <div style={{ 
              cursor: 'pointer',
              textAlign: 'center',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <UploadOutlined style={{ fontSize: '36px', color: primaryColor, marginBottom: '12px' }} />
              <div>
                <Text>点击或拖拽文件到此区域上传</Text>
              </div>
              <div style={{ marginTop: '12px' }}>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  style={{ width: '160px' }}
                >
                  选择音频文件
                </Button>
              </div>
              <div style={{ marginTop: '16px', color: '#666' }}>
                <Text>限时体验100小时</Text>
              </div>
              
              {uploadError && (
                <div style={{ 
                  marginTop: '16px', 
                  color: '#ff4d4f',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  padding: '8px 16px',
                  backgroundColor: '#fff2f0',
                  border: '1px solid #ffccc7',
                  borderRadius: '6px',
                  maxWidth: '300px'
                }}>
                  {uploadError}
                </div>
              )}
            </div>
          </Upload>
        </div>
      </div>
    );
  }
  
  // 文件上传后的界面
  return (
    <div style={{ maxWidth: '100%', width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 标题区域和上传新文件按钮 */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={4} style={{ margin: 0 }}>上传文件转写</Title>
          <Text type="secondary">支持mp3、wav、m4a、aac、mp4、wma、ogg、amr、flac等音频格式，文件大小不超过200MB</Text>
        </div>
        
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          {/* 如果是历史记录，显示删除按钮 */}
          {isHistoryRecord && recordId && onDeleteRecord && (
            <Popconfirm
              title="确认删除"
              description="确定要删除这条转写记录吗？删除后无法恢复。"
              onConfirm={() => onDeleteRecord(recordId)}
              okText="删除"
              cancelText="取消"
              okType="danger"
            >
              <Button 
                danger
                icon={<DeleteOutlined />}
                size="middle"
              >
                删除记录
              </Button>
            </Popconfirm>
          )}
          
          <Upload
            accept=".mp3,.wav,.m4a,.aac,.mp4,.wma,.ogg,.amr,.flac"
            maxCount={1}
            multiple={false}
            showUploadList={false}
            beforeUpload={handleBeforeUpload}
            onChange={onFileUpload}
          >
            <Button 
              icon={<UploadOutlined />}
              size="middle"
            >
              上传新文件
            </Button>
          </Upload>
        </div>
      </div>

      {/* Tab内容展示区域 */}
      <div 
        style={{ 
          background: '#fff', 
          border: '1px solid #f0f0f0',
          borderRadius: '8px',
          flex: 1,
          marginBottom: '16px',
          minHeight: '320px',
          display: 'flex',
          flexDirection: 'column'
        }}
        className="transcript-container"
      >
        {/* 固定的Tab栏 */}
        <div style={{ 
          borderBottom: '1px solid #f0f0f0',
          background: '#fff',
          borderRadius: '8px 8px 0 0'
        }}>
          <Tabs 
            activeKey={activeTab} 
            onChange={handleTabChange}
            tabBarStyle={{ 
              margin: 0, 
              paddingLeft: '16px', 
              paddingRight: '16px',
              borderBottom: 'none'
            }}
            items={[
              {
                key: 'transcript',
                label: (
                  <span>
                    <FileTextOutlined />
                    转写内容
                  </span>
                )
              },
              {
                key: 'summary',
                label: (
                  <span>
                    <BulbOutlined />
                    会议总结
                  </span>
                ),
                disabled: !hasSummary
              },
              {
                key: 'overview',
                label: (
                  <span>
                    <EyeOutlined />
                    会议速览
                  </span>
                ),
                disabled: !hasOverview
              }
            ]}
          />
        </div>
        
        {/* 可滚动的内容区域 */}
        <div style={{ 
          flex: 1, 
          display: 'flex', 
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          {/* 转写内容 */}
          {activeTab === 'transcript' && (
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* 导出按钮 */}
              <div style={{ padding: '12px 16px', borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'flex-end' }}>
                <Button 
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={onExportTranscriptPDF}
                  size="small"
                >
                  导出转写内容
                </Button>
              </div>
              
              {/* 转写内容区域 */}
              <div style={{ flex: 1, overflow: 'auto', padding: '16px' }}>
                {renderTranscriptContent()}
              </div>
            </div>
          )}
          
          {/* 会议总结 */}
          {activeTab === 'summary' && (
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* 导出按钮 */}
              <div style={{ padding: '12px 16px', borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'flex-end' }}>
                <Button 
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={() => onExportSummaryPDF && onExportSummaryPDF(summaryContent)}
                  size="small"
                  disabled={!summaryContent}
                >
                  导出总结内容
                </Button>
              </div>
              
              {/* 总结内容区域 */}
              <div style={{ flex: 1, overflow: 'auto', padding: '16px' }}>
                {renderMarkdownContent(summaryContent, <BulbOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />, '暂无总结内容')}
              </div>
            </div>
          )}
          
          {/* 会议速览 */}
          {activeTab === 'overview' && (
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* 导出按钮 */}
              <div style={{ padding: '12px 16px', borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'flex-end' }}>
                <Button 
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={() => onExportOverviewPDF && onExportOverviewPDF(overviewContent)}
                  size="small"
                  disabled={!overviewContent}
                >
                  导出速览内容
                </Button>
              </div>
              
              {/* 速览内容区域 */}
              <div style={{ flex: 1, overflow: 'auto', padding: '16px' }}>
                {renderMarkdownContent(overviewContent, <EyeOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />, '暂无速览内容')}
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* 底部处理状态展示区域 */}
      {isProcessing && (
        <div style={{ marginBottom: '16px' }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
            <ClockCircleOutlined style={{ marginRight: '8px', color: primaryColor }} />
            <Text strong>音频处理中</Text>
          </div>
          
          {/* 显示解码状态 */}
          {decodeStatus && (
            <div style={{ marginBottom: '8px' }}>
              <Text type="secondary" style={{ fontSize: '13px' }}>
                {decodeStatus}
              </Text>
            </div>
          )}
          
          {/* 显示解码进度条（仅当有进度时） */}
          {decodeProgress !== undefined && decodeProgress > 0 && (
            <div style={{ marginBottom: '8px' }}>
              <Progress 
                percent={decodeProgress} 
                status="active" 
                strokeColor={primaryColor} 
                size="small"
                format={percent => `${percent}%`}
              />
            </div>
          )}
          
          {/* 显示处理状态（非解码状态时） */}
          {!decodeStatus && (
            <div style={{ marginBottom: '8px' }}>
              <Text type="secondary" style={{ fontSize: '13px' }}>
                {processingStatus}
              </Text>
            </div>
          )}
          
          {/* 显示处理进度条（仅当不是解码阶段且进度大于0时） */}
          {!decodeStatus && processingProgress > 0 && (
            <Progress 
              percent={processingProgress} 
              status="active" 
              strokeColor={primaryColor} 
              size="small"
              format={percent => `${percent}%`}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default UploadPanel; 