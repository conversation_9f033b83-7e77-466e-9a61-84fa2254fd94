import { TranscriptMessage } from './AudioTranscriber';

// 历史记录列表项
export interface TranscriptionHistoryItem {
  id: number;
  title: string;
  type: 'file' | 'audio';
  user_id: number;
  task_id?: string;
  task_status?: string;
  overview_md?: string | null;
  summary_md?: string | null;
  created_at: string;
  modified_at: string;
}

// 历史记录列表响应
export interface TranscriptionListResponse {
  code: number;
  message: string;
  data: {
    items: TranscriptionHistoryItem[];
    total: number;
  };
}

// 单条历史记录详情
export interface TranscriptionDetailResponse {
  code: number;
  message: string;
  data: {
    id: number;
    title: string;
    type: 'file' | 'audio';
    content: {
      end: number;
      text: string;
      start: number;
    }[];
    user_id: number;
    created_at: string;
    modified_at: string;
  };
}

// 总结记录列表项
export interface SummaryRecord {
  id: number;
  name: string;
  transcription_id: number;
  created_at: string;
  modified_at: string;
}

// 总结列表响应
export interface SummaryListResponse {
  code: number;
  message: string;
  data: {
    items: SummaryRecord[];
    total: number;
  };
}

// 总结详情
export interface SummaryDetail {
  id: number;
  name: string;
  content: string;
  transcription_id: number;
  created_at: string;
  modified_at: string;
}

// 总结详情响应
export interface SummaryDetailResponse {
  code: number;
  message: string;
  data: SummaryDetail;
}

export interface Conversation {
  key: string;
  label: string;
  timestamp: number;
  messages: TranscriptMessage[];
  summary?: string;
  summary_md?: string;
  _hiddenSummary?: string; // 临时隐藏的总结内容
  overview_md?: string; // 添加速览内容字段
  fileInfo?: {
    name: string;
    uid: string;
  };
  recordingTime?: string;
  id?: number; // 后端记录ID
  serverId?: number; // 容错保存时的服务器记录ID
  taskId?: string; // 异步转写任务ID
  taskStatus?: string; // 任务状态
  type?: 'file' | 'audio'; // 记录类型
  isLocal?: boolean; // 添加isLocal字段，表示该会话是否为本地未保存的会话
  isManuallyLoaded?: boolean; // 标记是否为手动加载的会话（如从总结页面跳转）
}

export type TabType = 'recording' | 'upload' | 'summary' | 'translate'; 