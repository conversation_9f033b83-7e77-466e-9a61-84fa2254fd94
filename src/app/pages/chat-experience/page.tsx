'use client'
import React, { useState, useEffect, useRef, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Layout, Input, Button, List, Avatar, Typography, Divider, Card, Space, message } from 'antd';
import { SendOutlined, PlusOutlined, DeleteOutlined, LoadingOutlined, ArrowLeftOutlined, LogoutOutlined } from '@ant-design/icons';

const { Header, Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  lastUpdated: number;
}

// 创建一个包含useSearchParams的组件
const ChatContent: React.FC = () => {
  const searchParams = useSearchParams();
  const [modelInfo, setModelInfo] = useState({
    modelId: '',
    modelName: 'AI助手',
    modelProvider: ''
  });
  
  // 从sessionStorage获取模型信息
  useEffect(() => {
    const storedModel = sessionStorage.getItem('selectedModel');
    if (storedModel) {
      try {
        const parsedModel = JSON.parse(storedModel);
        setModelInfo({
          modelId: parsedModel.id || '',
          modelName: parsedModel.name || 'AI助手',
          modelProvider: parsedModel.provider || ''
        });
      } catch (error) {
        console.error('解析模型信息出错:', error);
      }
    }
  }, []);
  
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textAreaRef = useRef<any>(null);
  
  // 初始化聊天会话
  useEffect(() => {
    // 在实际应用中，这里可以从localStorage或后端API加载历史会话
    const defaultSession: ChatSession = {
      id: Date.now().toString(),
      title: '新对话',
      messages: [],
      lastUpdated: Date.now()
    };
    
    setSessions([defaultSession]);
    setCurrentSessionId(defaultSession.id);
  }, []);
  
  // 当消息更新时滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  // 创建新会话
  const createNewSession = () => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: '新对话',
      messages: [],
      lastUpdated: Date.now()
    };
    
    setSessions(prev => [newSession, ...prev]);
    setCurrentSessionId(newSession.id);
    setMessages([]);
  };
  
  // 切换会话
  const switchSession = (sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      setCurrentSessionId(sessionId);
      setMessages(session.messages);
    }
  };
  
  // 更新会话标题
  const updateSessionTitle = (sessionId: string, firstMessage: string) => {
    setSessions(prev => prev.map(session => {
      if (session.id === sessionId) {
        // 使用用户的第一条消息前20个字符作为标题
        const title = firstMessage.length > 20 
          ? firstMessage.substring(0, 20) + '...' 
          : firstMessage;
        
        return {
          ...session,
          title,
          lastUpdated: Date.now()
        };
      }
      return session;
    }));
  };
  
  // 删除会话
  const deleteSession = (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setSessions(prev => prev.filter(session => session.id !== sessionId));
    
    if (currentSessionId === sessionId) {
      if (sessions.length > 1) {
        const newCurrentSession = sessions.find(s => s.id !== sessionId);
        if (newCurrentSession) {
          setCurrentSessionId(newCurrentSession.id);
          setMessages(newCurrentSession.messages);
        }
      } else {
        createNewSession();
      }
    }
  };
  
  // 发送消息
  const sendMessage = async () => {
    if (!inputValue.trim() || isGenerating) return;
    
    const userMessage: Message = {
      role: 'user',
      content: inputValue.trim(),
      timestamp: Date.now()
    };
    
    // 更新当前会话的消息列表
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    
    // 如果是会话的第一条消息，更新会话标题
    if (messages.length === 0) {
      updateSessionTitle(currentSessionId, inputValue.trim());
    }
    
    // 更新会话中的消息
    setSessions(prev => prev.map(session => {
      if (session.id === currentSessionId) {
        return {
          ...session,
          messages: updatedMessages,
          lastUpdated: Date.now()
        };
      }
      return session;
    }));
    
    setInputValue('');
    setIsGenerating(true);
    
    try {
      // 模拟AI回复
      setTimeout(() => {
        const aiResponse: Message = {
          role: 'assistant',
          content: `这是来自 ${modelInfo.modelName} (${modelInfo.modelProvider}) 的回复:\n\n${generateRandomResponse(inputValue.trim())}`,
          timestamp: Date.now()
        };
        
        const newMessages = [...updatedMessages, aiResponse];
        setMessages(newMessages);
        
        // 更新会话中的消息
        setSessions(prev => prev.map(session => {
          if (session.id === currentSessionId) {
            return {
              ...session,
              messages: newMessages,
              lastUpdated: Date.now()
            };
          }
          return session;
        }));
        
        setIsGenerating(false);
      }, 1500);
      
    } catch (error) {
      console.error('Error generating response:', error);
      message.error('生成回复时出错，请重试');
      setIsGenerating(false);
    }
  };

  // 生成随机回复（仅用于演示）
  const generateRandomResponse = (query: string) => {
    return `感谢您的提问"${query}"。\n\nAI对话功能即将上线，我们正在努力完善中，请您耐心等待！`;
  };
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };
  
  return (
    <Layout style={{ height: 'calc(100vh - 50px)', flexDirection: 'column' }}>
      {/* 头部 - 模型信息 */}
      <Header style={{ 
        height: '64px', 
        padding: '0 24px', 
        background: '#fff', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => {
              // 返回模型超市，不需要清除URL参数
              
              // 触发菜单点击事件，切换到模型超市
              const menuClickEvent = new CustomEvent('menuClick', {
                detail: { key: 'model-market' }
              });
              document.body.dispatchEvent(menuClickEvent);
            }}
            style={{ marginRight: '16px' }}
            type="text"
          />
          <Title level={4} style={{ margin: 0 }}>
            模型体验：{modelInfo.modelName}
          </Title>
        </div>
      </Header>
      
      {/* 内容区域 - 上下布局改为左右布局 */}
      <Content style={{ display: 'flex', flexDirection: 'row', flex: 1, overflow: 'hidden' }}>
        {/* 侧边栏 - 历史会话 */}
        <div 
          style={{ 
            width: '250px',
            background: '#fff',
            borderRight: '1px solid #f0f0f0',
            height: '100%',
            overflow: 'auto',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <div style={{ padding: '16px' }}>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={createNewSession}
              block
            >
              开启新对话
            </Button>
          </div>
          
          <Divider style={{ margin: '0 0 8px 0' }} />
          
          <div style={{ padding: '0 16px' }}>
            <Text strong style={{ fontSize: '14px' }}>历史记录</Text>
          </div>
          
          <List
            itemLayout="horizontal"
            dataSource={sessions}
            style={{ marginTop: '8px', flex: 1, overflow: 'auto' }}
            renderItem={session => (
              <List.Item
                onClick={() => switchSession(session.id)}
                style={{ 
                  padding: '8px 16px',
                  cursor: 'pointer',
                  backgroundColor: currentSessionId === session.id ? '#f0f5ff' : 'transparent'
                }}
                actions={[
                  <DeleteOutlined 
                    key="delete" 
                    onClick={(e) => deleteSession(session.id, e)}
                    style={{ color: '#999' }}
                  />
                ]}
              >
                <Text 
                  ellipsis 
                  style={{ 
                    maxWidth: '180px',
                    color: currentSessionId === session.id ? '#1677ff' : 'inherit'
                  }}
                >
                  {session.title}
                </Text>
              </List.Item>
            )}
          />
        </div>
        
        {/* 聊天区域 */}
        <div style={{ 
          flex: 1, 
          display: 'flex', 
          flexDirection: 'column', 
          height: '100%',
          overflow: 'hidden'
        }}>
          {/* 消息列表 */}
          <div style={{ 
            flex: 1, 
            padding: '24px', 
            overflowY: 'auto',
            background: '#f7f7f7'
          }}>
            {messages.length === 0 ? (
              <div style={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                height: '100%',
                flexDirection: 'column'
              }}>
                <Text type="secondary" style={{ fontSize: '16px', marginBottom: '8px' }}>
                  开始与 {modelInfo.modelName} 的对话
                </Text>
                <Text type="secondary">
                  你可以询问任何问题，AI将为你提供回答
                </Text>
              </div>
            ) : (
              <>
                {messages.map((msg, index) => (
                  <div 
                    key={index} 
                    style={{ 
                      display: 'flex',
                      marginBottom: '24px',
                      flexDirection: msg.role === 'user' ? 'row-reverse' : 'row'
                    }}
                  >
                    <Avatar 
                      style={{ 
                        marginLeft: msg.role === 'user' ? '16px' : 0,
                        marginRight: msg.role === 'assistant' ? '16px' : 0,
                        background: msg.role === 'user' ? '#1677ff' : '#f56a00'
                      }}
                    >
                      {msg.role === 'user' ? '我' : 'AI'}
                    </Avatar>
                    
                    <Card 
                      style={{ 
                        maxWidth: '80%',
                        background: msg.role === 'user' ? '#e6f7ff' : '#fff'
                      }}
                      bodyStyle={{ padding: '12px 16px' }}
                    >
                      <Paragraph 
                        style={{ margin: 0, whiteSpace: 'pre-wrap' }}
                      >
                        {msg.content}
                      </Paragraph>
                    </Card>
                  </div>
                ))}
                
                {isGenerating && (
                  <div style={{ display: 'flex', marginBottom: '24px' }}>
                    <Avatar style={{ marginRight: '16px', background: '#f56a00' }}>
                      AI
                    </Avatar>
                    <Card bodyStyle={{ padding: '12px 16px' }}>
                      <Space>
                        <LoadingOutlined />
                        <Text type="secondary">正在生成回复...</Text>
                      </Space>
                    </Card>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </>
            )}
          </div>
          
          {/* 输入区域 */}
          <div style={{ 
            padding: '16px 24px', 
            borderTop: '1px solid #f0f0f0',
            background: '#fff'
          }}>
            <div style={{ 
              display: 'flex',
              alignItems: 'flex-end',
            }}>
              <TextArea
                ref={textAreaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={`给${modelInfo.modelName}发送消息`}
                autoSize={{ minRows: 1, maxRows: 5 }}
                style={{ flex: 1, marginRight: '16px' }}
                disabled={isGenerating}
              />
              <Button 
                type="primary" 
                icon={<SendOutlined />} 
                onClick={sendMessage}
                disabled={!inputValue.trim() || isGenerating}
                shape="circle"
                size="large"
              />
            </div>
            <div style={{ marginTop: '8px', textAlign: 'right' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                按 Enter 发送，Shift + Enter 换行
              </Text>
            </div>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

// 主页面组件，使用Suspense包裹使用useSearchParams的组件
const ChatExperiencePage: React.FC = () => {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <ChatContent />
    </Suspense>
  );
};

export default ChatExperiencePage; 