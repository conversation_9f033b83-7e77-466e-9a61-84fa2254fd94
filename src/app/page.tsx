'use client'
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import LoginPage from './pages/login/page';
import MainLayout from './layout/MainLayout';

export default function Home() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loading, setLoading] = useState(true);
  
  // 检查登录状态
  useEffect(() => {
    const token = localStorage.getItem('dy-token');
    setIsLoggedIn(!!token);
    setLoading(false);
  }, []);

  if (loading) {
    return <div className="App">Loading...</div>;
  }

  // 根据登录状态返回不同的组件
  return isLoggedIn ? <MainLayout /> : <LoginPage />;
}