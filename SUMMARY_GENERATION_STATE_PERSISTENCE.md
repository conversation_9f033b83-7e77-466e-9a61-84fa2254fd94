# 总结生成状态持久化功能

## 问题描述

用户反馈：正在生成总结时，若刷新页面，则刷新后虽然页面为新发起总结页，但实际上后台仍然在继续生成之前的总结。建议这种情况在切回到生成总结页时，仍然保持为正在生成的页面。

## 解决方案

实现了一个基于localStorage的总结生成状态持久化机制，确保页面刷新后能够恢复总结生成状态。

## 实现细节

### 1. 状态保存机制

在 `src/app/pages/audio-transcription/page.tsx` 中添加了以下功能：

#### 状态保存函数
```typescript
const saveSummaryGeneratingState = (isGenerating: boolean, recordId?: number) => {
  if (typeof window !== 'undefined') {
    if (isGenerating) {
      const state = {
        isGenerating: true,
        recordId: recordId,
        timestamp: Date.now()
      };
      localStorage.setItem('summaryGeneratingState', JSON.stringify(state));
    } else {
      localStorage.removeItem('summaryGeneratingState');
    }
  }
};
```

#### 状态恢复函数
```typescript
const getSavedSummaryGeneratingState = () => {
  if (typeof window !== 'undefined') {
    const savedState = localStorage.getItem('summaryGeneratingState');
    if (savedState) {
      try {
        const state = JSON.parse(savedState);
        // 检查状态是否过期（超过30分钟则认为过期）
        const now = Date.now();
        if (now - state.timestamp < 30 * 60 * 1000) {
          return state;
        } else {
          localStorage.removeItem('summaryGeneratingState');
        }
      } catch (error) {
        localStorage.removeItem('summaryGeneratingState');
      }
    }
  }
  return null;
};
```

### 2. 状态初始化

修改了 `generatingSummary` 状态的初始化，从localStorage恢复状态：

```typescript
const [generatingSummary, setGeneratingSummary] = useState<boolean>(() => {
  // 从localStorage恢复总结生成状态
  if (typeof window !== 'undefined') {
    const savedState = localStorage.getItem('summaryGeneratingState');
    if (savedState) {
      try {
        const state = JSON.parse(savedState);
        // 检查状态是否过期（超过30分钟则认为过期）
        const now = Date.now();
        if (now - state.timestamp < 30 * 60 * 1000) {
          return state.isGenerating;
        } else {
          localStorage.removeItem('summaryGeneratingState');
        }
      } catch (error) {
        localStorage.removeItem('summaryGeneratingState');
      }
    }
  }
  return false;
});
```

### 3. 状态保存时机

在总结生成的关键时刻保存状态：

1. **开始生成时**：在 `handleGenerateSummary` 函数中调用 `saveSummaryGeneratingState(true, recordId)`
2. **生成完成时**：在 `.finally()` 块中调用 `saveSummaryGeneratingState(false)`
3. **生成失败时**：在 `.catch()` 块中调用 `saveSummaryGeneratingState(false)`
4. **无效操作时**：在错误处理中调用 `saveSummaryGeneratingState(false)`

### 4. 页面恢复逻辑

在组件挂载时检查并恢复状态：

```typescript
useEffect(() => {
  // 检查是否有保存的总结生成状态需要恢复
  const savedState = getSavedSummaryGeneratingState();
  if (savedState && savedState.isGenerating) {
    console.log('检测到保存的总结生成状态，准备恢复:', savedState);
    // 切换到总结页面
    setActiveTab('summary');
    // 如果有recordId，尝试恢复生成状态
    if (savedState.recordId) {
      setTimeout(() => {
        console.log('恢复总结生成状态，recordId:', savedState.recordId);
        // 为了安全起见，我们只恢复UI状态，不重新开始生成
        // 用户可以手动重新生成
      }, 100);
    }
  }
}, []);
```

### 5. SummaryPanel组件适配

在 `src/app/pages/audio-transcription/components/SummaryPanel.tsx` 中：

#### 初始状态恢复
```typescript
const [showFileSelection, setShowFileSelection] = useState<boolean>(() => {
  // 检查是否有保存的总结生成状态
  if (typeof window !== 'undefined') {
    const savedState = localStorage.getItem('summaryGeneratingState');
    if (savedState) {
      try {
        const state = JSON.parse(savedState);
        const now = Date.now();
        if (now - state.timestamp < 30 * 60 * 1000 && state.isGenerating) {
          return false; // 如果正在生成总结，隐藏文件选择界面
        }
      } catch (error) {
        console.error('SummaryPanel解析总结生成状态失败:', error);
      }
    }
  }
  return true;
});
```

#### 状态监听
添加了定期检查localStorage状态的逻辑，确保UI能够实时反映状态变化。

#### 显示逻辑优化
修改了正在生成状态的判断条件，同时检查props和localStorage：

```typescript
) : (isGenerating || (() => {
  // 检查localStorage中是否有正在生成的状态
  if (typeof window !== 'undefined') {
    const savedState = localStorage.getItem('summaryGeneratingState');
    if (savedState) {
      try {
        const state = JSON.parse(savedState);
        const now = Date.now();
        return now - state.timestamp < 30 * 60 * 1000 && state.isGenerating;
      } catch (error) {
        return false;
      }
    }
  }
  return false;
})()) ? (
```

## 功能特性

1. **状态持久化**：总结生成状态保存在localStorage中，页面刷新后不会丢失
2. **自动过期**：状态有30分钟的有效期，避免长期无效状态
3. **安全恢复**：页面刷新后只恢复UI状态，不会重新发起API请求
4. **实时同步**：SummaryPanel组件能够实时反映状态变化
5. **错误处理**：完善的错误处理机制，确保状态一致性

## 用户体验改进

- 用户在总结生成过程中刷新页面，会自动跳转到总结页面并显示正在生成状态
- 避免了用户误以为总结生成被中断的困惑
- 保持了后台生成和前端显示的一致性

## 注意事项

1. 状态只在浏览器端保存，不会影响服务器端逻辑
2. 30分钟后状态自动过期，避免长期占用存储空间
3. 页面刷新后不会重新发起生成请求，只恢复显示状态
4. 用户可以在恢复状态后手动重新生成总结
