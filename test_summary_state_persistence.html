<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总结生成状态持久化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.generating {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .status.idle {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>总结生成状态持久化测试</h1>
    
    <div class="test-section">
        <h2>功能测试</h2>
        <p>这个测试页面模拟了总结生成状态的保存和恢复功能。</p>
        
        <div id="status" class="status idle">
            状态：空闲
        </div>
        
        <button onclick="startGenerating()">开始生成总结</button>
        <button onclick="stopGenerating()">停止生成</button>
        <button onclick="checkState()">检查保存的状态</button>
        <button onclick="clearState()">清除状态</button>
        <button onclick="simulateRefresh()">模拟页面刷新</button>
        
        <div class="log" id="log"></div>
    </div>
    
    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>点击"开始生成总结"按钮</li>
            <li>观察状态变为"正在生成"</li>
            <li>点击"模拟页面刷新"按钮</li>
            <li>观察状态是否保持为"正在生成"</li>
            <li>等待30分钟后再次检查状态（应该自动过期）</li>
        </ol>
    </div>

    <script>
        // 模拟总结生成状态管理
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(text, className) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = `状态：${text}`;
            statusElement.className = `status ${className}`;
        }

        function saveSummaryGeneratingState(isGenerating, recordId) {
            if (isGenerating) {
                const state = {
                    isGenerating: true,
                    recordId: recordId || 123,
                    timestamp: Date.now()
                };
                localStorage.setItem('summaryGeneratingState', JSON.stringify(state));
                log(`保存状态到localStorage: ${JSON.stringify(state)}`);
            } else {
                localStorage.removeItem('summaryGeneratingState');
                log('清除localStorage中的状态');
            }
        }

        function getSavedSummaryGeneratingState() {
            const savedState = localStorage.getItem('summaryGeneratingState');
            if (savedState) {
                try {
                    const state = JSON.parse(savedState);
                    const now = Date.now();
                    if (now - state.timestamp < 30 * 60 * 1000) {
                        log(`从localStorage恢复状态: ${JSON.stringify(state)}`);
                        return state;
                    } else {
                        localStorage.removeItem('summaryGeneratingState');
                        log('状态已过期，自动清除');
                        return null;
                    }
                } catch (error) {
                    localStorage.removeItem('summaryGeneratingState');
                    log(`解析状态失败: ${error.message}`);
                    return null;
                }
            }
            return null;
        }

        function startGenerating() {
            updateStatus('正在生成总结...', 'generating');
            saveSummaryGeneratingState(true, 123);
            log('开始生成总结');
        }

        function stopGenerating() {
            updateStatus('空闲', 'idle');
            saveSummaryGeneratingState(false);
            log('停止生成总结');
        }

        function checkState() {
            const state = getSavedSummaryGeneratingState();
            if (state) {
                log(`当前保存的状态: ${JSON.stringify(state)}`);
                if (state.isGenerating) {
                    updateStatus('正在生成总结...', 'generating');
                }
            } else {
                log('没有保存的状态');
                updateStatus('空闲', 'idle');
            }
        }

        function clearState() {
            localStorage.removeItem('summaryGeneratingState');
            updateStatus('空闲', 'idle');
            log('手动清除状态');
        }

        function simulateRefresh() {
            log('模拟页面刷新...');
            // 清除当前显示状态
            updateStatus('加载中...', 'idle');
            
            // 模拟页面重新加载后的状态恢复
            setTimeout(() => {
                const savedState = getSavedSummaryGeneratingState();
                if (savedState && savedState.isGenerating) {
                    updateStatus('正在生成总结...', 'generating');
                    log('页面刷新后恢复到生成状态');
                } else {
                    updateStatus('空闲', 'idle');
                    log('页面刷新后恢复到空闲状态');
                }
            }, 500);
        }

        // 页面加载时检查状态
        window.onload = function() {
            log('页面加载完成');
            checkState();
        };
    </script>
</body>
</html>
