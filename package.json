{"name": "<PERSON><PERSON><PERSON>-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^6.0.0", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/x": "^1.2.0", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@types/file-saver": "^2.0.7", "antd": "^5.25.2", "file-saver": "^2.0.5", "github-markdown-css": "^5.8.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "marked": "^15.0.12", "md-to-pdf": "^5.2.4", "next": "15.3.2", "puppeteer": "^24.9.0", "puppeteer-core": "^24.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-markdown": "^10.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}